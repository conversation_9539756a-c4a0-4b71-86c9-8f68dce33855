#include <map>
#include <string>
inline const char* getPouchofhead(int Id) {
    switch (Id) {
    case 9804001:
    return "1级头盔";
    break;
case 9804002:
    return "2级头盔";
    break;
case 9804003:
    return "3级头盔";
    break;
case 9804004:
    return "4级头盔";
    break;
case 9804005:
    return "4级头盔(铁爪)";
    break;
case 9804006:
    return "4级头盔(黑鹰)";
    break;
case 9804007:
    return "5级头盔";
    break;
case 9804008:
    return "5级头盔(铁爪)";
    break;
case 9804009:
    return "5级头盔(黑鹰)";
    break;
case 9804010:
    return "6级头盔";
    break;
case 9804011:
    return "6级头盔(铁爪)";
    break;
case 9804012:
    return "6级头盔(黑鹰)";
    break;
case 9804013:
    return "7级头盔";
    break;
case 9804014:
    return "7级头盔(铁爪)";
    break;
case 9804015:
    return "7级头盔(黑鹰)";
    break;
case 9804016:
    return "头戴夜视仪";
    break;
case 9804017:
    return "7级头盔";
    break;
case 9804018:
    return "7级头盔(铁爪)";
    break;
case 9804019:
    return "7级头盔(黑鹰)";
    break;
case 9804020:
    return "机甲腿甲（冲刺技能）";
    break;
case 9805001:
    return "1级防弹衣";
    break;
case 9805002:
    return "2级防弹衣";
    break;
case 9805003:
    return "3级防弹衣";
    break;
case 9805004:
    return "4级防弹衣";
    break;
case 9805005:
    return "4级防弹衣(铁爪)";
    break;
case 9805006:
    return "4级防弹衣(黑鹰)";
    break;
case 9805007:
    return "5级防弹衣";
    break;
case 9805008:
    return "5级防弹衣(铁爪)";
    break;
case 9805009:
    return "5级防弹衣(黑鹰)";
    break;
case 9805010:
    return "6级防弹衣";
    break;
case 9805011:
    return "6级防弹衣(铁爪)";
    break;
case 9805012:
    return "6级防弹衣(黑鹰)";
    break;
case 9805013:
    return "7级防弹衣";
    break;
case 9805014:
    return "7级防弹衣(铁爪)";
    break;
case 9805015:
    return "7级防弹衣(黑鹰)";
    break;
case 9805016:
    return "7级防弹衣";
    break;
case 9805017:
    return "7级防弹衣(铁爪)";
    break;
case 9805018:
    return "7级防弹衣(黑鹰)";
    break;
case 9805020:
    return "机甲防具";
    break;
case 9805021:
    return "子物品-4级防弹衣";
    break;
case 9805022:
    return "子物品-4级防弹衣(铁爪)";
    break;
case 9805023:
    return "子物品-4级防弹衣(黑鹰)";
    break;
case 9805024:
    return "子物品-5级防弹衣";
    break;
case 9805025:
    return "子物品-5级防弹衣(铁爪)";
    break;
case 9805026:
    return "子物品-5级防弹衣(黑鹰)";
    break;
case 9805027:
    return "子物品-6级防弹衣";
    break;
case 9805028:
    return "子物品-6级防弹衣(铁爪)";
    break;
case 9805029:
    return "子物品-6级防弹衣(黑鹰)";
    break;
case 9805030:
    return "子物品-7级防弹衣";
    break;
case 9805031:
    return "子物品-7级防弹衣(铁爪)";
    break;
case 9805032:
    return "子物品-7级防弹衣(黑鹰)";
    break;
case 9805033:
    return "子物品-7级防弹衣";
    break;
case 9805034:
    return "子物品-7级防弹衣(铁爪)";
    break;
case 9805035:
    return "子物品-7级防弹衣(黑鹰)";
    break;
case 9805098:
    return "子物品-7级防弹衣·特劳斯";
    break;
case 9805099:
    return "7级防弹衣·特劳斯";
    break;
case 9806001:
    return "1级背包";
    break;
case 9806002:
    return "2级背包";
    break;
case 9806003:
    return "3级背包";
    break;
case 9806004:
    return "4级背包";
    break;
case 9806005:
    return "4级背包(铁爪)";
    break;
case 9806006:
    return "4级背包(黑鹰)";
    break;
case 9806007:
    return "5级背包";
    break;
case 9806008:
    return "5级背包(铁爪)";
    break;
case 9806009:
    return "5级背包(黑鹰)";
    break;
case 9806010:
    return "6级背包";
    break;
case 9806011:
    return "6级背包(铁爪)";
    break;
case 9806012:
    return "6级背包(黑鹰)";
    break;
case 9806013:
    return "7级背包";
    break;
case 9806014:
    return "7级背包(铁爪)";
    break;
case 9806015:
    return "7级背包(黑鹰)";
    break;
case 9806016:
    return "7级背包";
    break;
case 9806017:
    return "7级背包(铁爪)";
    break;
case 9806018:
    return "7级背包(黑鹰)";
    break;
default:
            return "NULL";
        break;
    
    }
}
inline const char* GetItems(int Id) {
    switch (Id) {
        case 9801001:          
            return "卧底证";
            break;
        case 9801002:
            return "能源电池";
            break;
        case 9801003:
            return "加密房间房卡";
            break;
        case 9801004:
            return "勘察情报";
            break;
        case 9802001:
            return "金库密码卡-白";
            break;
        case 9802002:
            return "金库密码卡-黄";
            break;
        case 9802003:
            return "金库密码卡-红";
            break;
        case 9802004:
            return "金库密码卡-绿";
            break;
        case 9802005:
            return "金库密码卡-黑";
            break;
        case 9802006:
            return "功勋奖章（铜）";
            break;
        case 9802007:
            return "功勋奖章（银）";
            break;
        case 9802008:
            return "功勋奖章（金）";
            break;
        case 9802009:
            return "撤离信号电话机";
            break;
        case 9803001:
            return "1级变卖物测试";
            break;
        case 9803002:
            return "2级变卖物测试";
            break;
        case 9803003:
            return "3级变卖物测试";
            break;
        case 9803004:
            return "4级变卖物测试";
            break;
        case 9803005:
            return "5级变卖物测试";
            break;
        case 9803006:
            return "6级变卖物测试";
            break;
        case 9803007:
            return "7级变卖物测试";
            break;
        case 9803008:
            return "指南针";
            break;
        case 9803009:
            return "明信片";
            break;
        case 9803010:
            return "旧式录像带";
            break;
        case 9803011:
            return "扑克牌";
            break;
        case 9803012:
            return "军用水壶";
            break;
        case 9803013:
            return "罐头";
            break;
        case 9803014:
            return "杂志";
            break;
        case 9803015:
            return "便携手礼";
            break;
        case 9803016:
            return "旅行手册";
            break;
        case 9803017:
            return "钱";
            break;
        case 9803018:
            return "零件包";
            break;
        case 9803019:
            return "机油";
            break;
        case 9803020:
            return "怀表";
            break;
        case 9803021:
            return "怡神精油";
            break;
        case 9803022:
            return "爱心项链";
            break;
        case 9803023:
            return "燃气瓶";
            break;
        case 9803024:
            return "润滑油";
            break;
        case 9803025:
            return "汽车钥匙";
            break;
        case 9803026:
            return "柴油";
            break;
        case 9803027:
            return "军用手表";
            break;
        case 9803028:
            return "高级钱";
            break;
        case 9803029:
            return "科技部件";
            break;
        case 9803030:
            return "狗牌";
            break;
        case 9803031:
            return "信号发生器";
            break;
        case 9803032:
            return "净水器";
            break;
        case 9803033:
            return "CPU处理器";
            break;
        case 9803034:
            return "探测器";
            break;
        case 9803035:
            return "平板电脑";
            break;
        case 9803036:
            return "军用电池";
            break;
        case 9803037:
            return "军用电路板";
            break;
        case 9803038:
            return "精密仪器蓝图";
            break;
        case 9803039:
            return "绝密情报";
            break;
        case 9803040:
            return "设计图纸";
            break;
        case 9803041:
            return "显卡";
            break;
        case 9803042:
            return "镜头";
            break;
        case 9803043:
            return "珍稀古钱币";
            break;
        case 9803044:
            return "一根金条";
            break;
        case 9803045:
            return "光子鸡奖杯";
            break;
        case 9803046:
            return "野兽碎骨";
            break;
        case 9803047:
            return "野兽头骨";
            break;
        case 9803048:
            return "异变的肩骨";
            break;
        case 9803049:
            return "装甲碎片";
            break;
        case 9803050:
            return "装甲能源部件";
            break;
        case 9803051:
            return "装甲爆发部件";
            break;
        case 9803052:
            return "紫矿碎片";
            break;
        case 9803053:
            return "紫矿晶块";
            break;
        case 9803054:
            return "稀有宝石";
            break;
        case 9803055:
            return "黄金烤鸡";
            break;
        case 9803056:
            return "收发信器";
            break;
        case 9803057:
            return "钛合金";
            break;
        case 9803058:
            return "制动引擎";
            break;
        case 9803059:
            return "胶卷";
            break;
        case 9803060:
            return "拉刀";
            break;
        case 9803061:
            return "打火机";
            break;
        case 9803062:
            return "黑胶唱片";
            break;
        case 9803063:
            return "窃听装置";
            break;
        case 9803064:
            return "摄像头";
            break;
        case 9803065:
            return "雷管";
            break;
        case 9803066:
            return "黑鹰勋章";
            break;
        case 9803067:
            return "金狼雕像";
            break;
        case 9803068:
            return "密信";
            break;
        case 9803069:
            return "紫色试管药剂";
            break;
        case 9803070:
            return "数据芯片";
            break;
        case 9804001:
            return "1级头盔";
            break;
        case 9804002:
            return "2级头盔";
            break;
        case 9804003:
            return "3级头盔";
            break;
        case 9804004:
            return "4级头盔";
            break;
        case 9804005:
            return "4级头盔(铁爪)";
            break;
        case 9804006:
            return "4级头盔(黑鹰)";
            break;
        case 9804007:
            return "5级头盔";
            break;
        case 9804008:
            return "5级头盔(铁爪)";
            break;
        case 9804009:
            return "5级头盔(黑鹰)";
            break;
        case 9804010:
            return "6级头盔";
            break;
        case 9804011:
            return "6级头盔(铁爪)";
            break;
        case 9804012:
            return "6级头盔(黑鹰)";
            break;
        case 9804013:
            return "7级头盔";
            break;
        case 9804014:
            return "7级头盔(铁爪)";
            break;
        case 9804015:
            return "7级头盔(黑鹰)";
            break;
        case 9804016:
            return "头戴夜视仪";
            break;
        case 9804017:
            return "7级头盔";
            break;
        case 9804018:
            return "7级头盔(铁爪)";
            break;
        case 9804019:
            return "7级头盔(黑鹰)";
            break;
        case 9804020:
            return "机甲腿甲（冲刺技能）";
            break;
        case 9805001:
            return "1级防弹衣";
            break;
        case 9805002:
            return "2级防弹衣";
            break;
        case 9805003:
            return "3级防弹衣";
            break;
        case 9805004:
            return "4级防弹衣";
            break;
        case 9805005:
            return "4级防弹衣(铁爪)";
            break;
        case 9805006:
            return "4级防弹衣(黑鹰)";
            break;
        case 9805007:
            return "5级防弹衣";
            break;
        case 9805008:
            return "5级防弹衣(铁爪)";
            break;
        case 9805009:
            return "5级防弹衣(黑鹰)";
            break;
        case 9805010:
            return "6级防弹衣";
            break;
        case 9805011:
            return "6级防弹衣(铁爪)";
            break;
        case 9805012:
            return "6级防弹衣(黑鹰)";
            break;
        case 9805013:
            return "7级防弹衣";
            break;
        case 9805014:
            return "7级防弹衣(铁爪)";
            break;
        case 9805015:
            return "7级防弹衣(黑鹰)";
            break;
        case 9805016:
            return "7级防弹衣";
            break;
        case 9805017:
            return "7级防弹衣(铁爪)";
            break;
        case 9805018:
            return "7级防弹衣(黑鹰)";
            break;
        case 9805020:
            return "机甲防具";
            break;
        case 9805021:
            return "子物品-4级防弹衣";
            break;
        case 9805022:
            return "子物品-4级防弹衣(铁爪)";
            break;
        case 9805023:
            return "子物品-4级防弹衣(黑鹰)";
            break;
        case 9805024:
            return "子物品-5级防弹衣";
            break;
        case 9805025:
            return "子物品-5级防弹衣(铁爪)";
            break;
        case 9805026:
            return "子物品-5级防弹衣(黑鹰)";
            break;
        case 9805027:
            return "子物品-6级防弹衣";
            break;
        case 9805028:
            return "子物品-6级防弹衣(铁爪)";
            break;
        case 9805029:
            return "子物品-6级防弹衣(黑鹰)";
            break;
        case 9805030:
            return "子物品-7级防弹衣";
            break;
        case 9805031:
            return "子物品-7级防弹衣(铁爪)";
            break;
        case 9805032:
            return "子物品-7级防弹衣(黑鹰)";
            break;
        case 9805033:
            return "子物品-7级防弹衣";
            break;
        case 9805034:
            return "子物品-7级防弹衣(铁爪)";
            break;
        case 9805035:
            return "子物品-7级防弹衣(黑鹰)";
            break;
        case 9805098:
            return "子物品-7级防弹衣·特劳斯";
            break;
        case 9805099:
            return "7级防弹衣·特劳斯";
            break;
        case 9806001:
            return "1级背包";
            break;
        case 9806002:
            return "2级背包";
            break;
        case 9806003:
            return "3级背包";
            break;
        case 9806004:
            return "4级背包";
            break;
        case 9806005:
            return "4级背包(铁爪)";
            break;
        case 9806006:
            return "4级背包(黑鹰)";
            break;
        case 9806007:
            return "5级背包";
            break;
        case 9806008:
            return "5级背包(铁爪)";
            break;
        case 9806009:
            return "5级背包(黑鹰)";
            break;
        case 9806010:
            return "6级背包";
            break;
        case 9806011:
            return "6级背包(铁爪)";
            break;
        case 9806012:
            return "6级背包(黑鹰)";
            break;
        case 9806013:
            return "7级背包";
            break;
        case 9806014:
            return "7级背包(铁爪)";
            break;
        case 9806015:
            return "7级背包(黑鹰)";
            break;
        case 9806016:
            return "7级背包";
            break;
        case 9806017:
            return "7级背包(铁爪)";
            break;
        case 9806018:
            return "7级背包(黑鹰)";
            break;
        case 9807001:
            return "大砍刀";
            break;
        case 9807002:
            return "撬棍";
            break;
        case 9807003:
            return "镰刀";
            break;
        case 9807004:
            return "平底锅";
            break;
        case 9807005:
            return "火焰刀·维列";
            break;
        case 9808001:
            return "P92手枪(破损)";
            break;
        case 9808002:
            return "P92手枪(修复)";
            break;
        case 9808003:
            return "P92手枪(完好)";
            break;
        case 9808008:
            return "P1911手枪(破损)";
            break;
        case 9808009:
            return "P1911手枪(修复)";
            break;
        case 9808010:
            return "P1911手枪(完好)";
            break;
        case 9808015:
            return "R1895手枪(破损)";
            break;
        case 9808016:
            return "R1895手枪(修复)";
            break;
        case 9808017:
            return "R1895手枪(完好)";
            break;
        case 9808022:
            return "P18C手枪(破损)";
            break;
        case 9808023:
            return "P18C手枪(修复)";
            break;
        case 9808024:
            return "P18C手枪(完好)";
            break;
        case 9808029:
            return "R45手枪(破损)";
            break;
        case 9808030:
            return "R45手枪(修复)";
            break;
        case 9808031:
            return "R45手枪(完好)";
            break;
        case 9808036:
            return "短管霰弹枪(破损)";
            break;
        case 9808037:
            return "短管霰弹枪(修复)";
            break;
        case 9808038:
            return "短管霰弹枪(完好)";
            break;
        case 9808043:
            return "蝎式手枪(修复)";
            break;
        case 9808044:
            return "蝎式手枪(完好)";
            break;
        case 9808045:
            return "蝎式手枪(改进)";
            break;
        case 9808046:
            return "蝎式手枪(破损)";
            break;
        case 9808050:
            return "沙漠之鹰手枪(修复)";
            break;
        case 9808051:
            return "沙漠之鹰手枪(完好)";
            break;
        case 9808052:
            return "沙漠之鹰手枪(改进)";
            break;
        case 9808053:
            return "沙漠之鹰手枪(破损)";
            break;
        case 9808057:
            return "TMP-9手枪(修复)";
            break;
        case 9808058:
            return "TMP-9手枪(完好)";
            break;
        case 9808059:
            return "TMP-9手枪(改进)";
            break;
        case 9808060:
            return "空投信号枪";
            break;
        case 9808061:
            return "TMP-9手枪(破损)";
            break;
        case 9809001:
            return "十字弩(破损)";
            break;
        case 9809002:
            return "十字弩(修复)";
            break;
        case 9809003:
            return "十字弩(完好)";
            break;
        case 9809009:
            return "爆炸猎弓(骑警Boss)";
            break;
        case 9809010:
            return "爆炸猎弓·杰西";
            break;
        case 9810001:
            return "S686霰弹枪(破损)";
            break;
        case 9810002:
            return "S686霰弹枪(修复)";
            break;
        case 9810003:
            return "S686霰弹枪(完好)";
            break;
        case 9810008:
            return "S1897霰弹枪(破损)";
            break;
        case 9810009:
            return "S1897霰弹枪(修复)";
            break;
        case 9810010:
            return "S1897霰弹枪(完好)";
            break;
        case 9810015:
            return "S12K霰弹枪(精制)";
            break;
        case 9810016:
            return "S12K霰弹枪(修复)";
            break;
        case 9810017:
            return "S12K霰弹枪(完好)";
            break;
        case 9810018:
            return "S12K霰弹枪(改进)";
            break;
        case 9810022:
            return "DBS霰弹枪(修复)";
            break;
        case 9810023:
            return "DBS霰弹枪(完好)";
            break;
        case 9810024:
            return "DBS霰弹枪(改进)";
            break;
        case 9810025:
            return "DBS霰弹枪(精制)";
            break;
        case 9810029:
            return "SPAS-12霰弹枪(修复)";
            break;
        case 9810030:
            return "SPAS-12霰弹枪(完好)";
            break;
        case 9810031:
            return "SPAS-12霰弹枪(改进)";
            break;
        case 9810032:
            return "SPAS-12霰弹枪(精制)";
            break;
        case 9810036:
            return "AA12-G霰弹枪(修复)";
            break;
        case 9810037:
            return "AA12-G霰弹枪(完好)";
            break;
        case 9810038:
            return "AA12-G霰弹枪(改进)";
            break;
        case 9810041:
            return "AA12-G霰弹枪(精制)";
            break;
        case 9811001:
            return "UZI冲锋枪(破损)";
            break;
        case 9811002:
            return "UZI冲锋枪(修复)";
            break;
        case 9811003:
            return "UZI冲锋枪(完好)";
            break;
        case 9811004:
            return "UZI冲锋枪(改进)";
            break;
        case 9811008:
            return "UMP45冲锋枪(破损)";
            break;
        case 9811009:
            return "UMP45冲锋枪(修复)";
            break;
        case 9811010:
            return "UMP45冲锋枪(完好)";
            break;
        case 9811011:
            return "UMP45冲锋枪(改进)";
            break;
        case 9811015:
            return "Vector冲锋枪(卓越)";
            break;
        case 9811016:
            return "Vector冲锋枪(完好)";
            break;
        case 9811017:
            return "Vector冲锋枪(改进)";
            break;
        case 9811018:
            return "Vector冲锋枪(精制)";
            break;
        case 9811019:
            return "Vector冲锋枪(卓越)";
            break;
        case 9811020:
            return "Vector冲锋枪(黑鹰)";
            break;
        case 9811021:
            return "Vector冲锋枪(铁爪)";
            break;
        case 9811022:
            return "汤姆逊冲锋枪(破损)";
            break;
        case 9811023:
            return "汤姆逊冲锋枪(修复)";
            break;
        case 9811024:
            return "汤姆逊冲锋枪(完好)";
            break;
        case 9811025:
            return "汤姆逊冲锋枪(改进)";
            break;
        case 9811029:
            return "野牛冲锋枪(破损)";
            break;
        case 9811030:
            return "野牛冲锋枪(修复)";
            break;
        case 9811031:
            return "野牛冲锋枪(完好)";
            break;
        case 9811036:
            return "MP5K冲锋枪(卓越)";
            break;
        case 9811037:
            return "MP5K冲锋枪(完好)";
            break;
        case 9811038:
            return "MP5K冲锋枪(改进)";
            break;
        case 9811039:
            return "MP5K冲锋枪(精制)";
            break;
        case 9811040:
            return "MP5K冲锋枪(卓越)";
            break;
        case 9811041:
            return "MP5K冲锋枪(黑鹰)";
            break;
        case 9811042:
            return "MP5K冲锋枪(铁爪)";
            break;
        case 9811043:
            return "AKS-74U冲锋枪(卓越)";
            break;
        case 9811044:
            return "AKS-74U冲锋枪(完好)";
            break;
        case 9811045:
            return "AKS-74U冲锋枪(改进)";
            break;
        case 9811046:
            return "AKS-74U冲锋枪(精制)";
            break;
        case 9811047:
            return "AKS-74U冲锋枪(卓越)";
            break;
        case 9811048:
            return "AKS-74U冲锋枪(铁爪)";
            break;
        case 9811049:
            return "AKS-74U冲锋枪(黑鹰)";
            break;
        case 9811050:
            return "P90冲锋枪(卓越)";
            break;
        case 9811051:
            return "P90冲锋枪(完好)";
            break;
        case 9811052:
            return "P90冲锋枪(改进)";
            break;
        case 9811053:
            return "P90冲锋枪(精制)";
            break;
        case 9811054:
            return "P90冲锋枪(卓越)";
            break;
        case 9811055:
            return "P90冲锋枪(黑鹰)";
            break;
        case 9811056:
            return "P90冲锋枪(铁爪)";
            break;
        case 9812002:
            return "AKM突击步枪(卓越)";
            break;
        case 9812003:
            return "AKM突击步枪(完好)";
            break;
        case 9812004:
            return "AKM突击步枪(改进)";
            break;
        case 9812005:
            return "AKM突击步枪(精制)";
            break;
        case 9812006:
            return "AKM突击步枪(卓越)";
            break;
        case 9812007:
            return "AKM突击步枪(黑鹰)";
            break;
        case 9812008:
            return "AKM突击步枪(铁爪)";
            break;
        case 9812009:
            return "M16A4突击步枪(破损)";
            break;
        case 9812010:
            return "M16A4突击步枪(修复)";
            break;
        case 9812011:
            return "M16A4突击步枪(完好)";
            break;
        case 9812012:
            return "M16A4突击步枪(改进)";
            break;
        case 9812015:
            return "SCAR-L突击步枪(卓越)";
            break;
        case 9812016:
            return "SCAR-L突击步枪(完好)";
            break;
        case 9812017:
            return "SCAR-L突击步枪(改进)";
            break;
        case 9812018:
            return "SCAR-L突击步枪(精制)";
            break;
        case 9812019:
            return "SCAR-L突击步枪(卓越)";
            break;
        case 9812020:
            return "SCAR-L突击步枪(黑鹰)";
            break;
        case 9812021:
            return "SCAR-L突击步枪(铁爪)";
            break;
        case 9812022:
            return "M416突击步枪(铁爪)";
            break;
        case 9812023:
            return "M416突击步枪(卓越)";
            break;
        case 9812024:
            return "M416突击步枪(完好)";
            break;
        case 9812025:
            return "M416突击步枪(改进)";
            break;
        case 9812026:
            return "M416突击步枪(精制)";
            break;
        case 9812027:
            return "M416突击步枪(卓越)";
            break;
        case 9812028:
            return "M416突击步枪(黑鹰)";
            break;
        case 9812029:
            return "GROZA突击步枪(卓越)";
            break;
        case 9812030:
            return "GROZA突击步枪(改进)";
            break;
        case 9812031:
            return "GROZA突击步枪(精制)";
            break;
        case 9812032:
            return "GROZA突击步枪(卓越)";
            break;
        case 9812033:
            return "GROZA突击步枪(黑鹰)";
            break;
        case 9812034:
            return "GROZA突击步枪(铁爪)";
            break;
        case 9812036:
            return "AUG突击步枪(卓越)";
            break;
        case 9812037:
            return "AUG突击步枪(改进)";
            break;
        case 9812038:
            return "AUG突击步枪(精制)";
            break;
        case 9812039:
            return "AUG突击步枪(卓越)";
            break;
        case 9812040:
            return "AUG突击步枪(黑鹰)";
            break;
        case 9812041:
            return "AUG突击步枪(铁爪)";
            break;
        case 9812043:
            return "QBZ突击步枪(卓越)";
            break;
        case 9812044:
            return "QBZ突击步枪(完好)";
            break;
        case 9812045:
            return "QBZ突击步枪(改进)";
            break;
        case 9812046:
            return "QBZ突击步枪(精制)";
            break;
        case 9812047:
            return "QBZ突击步枪(卓越)";
            break;
        case 9812048:
            return "QBZ突击步枪(黑鹰)";
            break;
        case 9812049:
            return "QBZ突击步枪(铁爪)";
            break;
        case 9812050:
            return "M762突击步枪(卓越)";
            break;
        case 9812051:
            return "M762突击步枪(完好)";
            break;
        case 9812052:
            return "M762突击步枪(改进)";
            break;
        case 9812053:
            return "M762突击步枪(精制)";
            break;
        case 9812054:
            return "M762突击步枪(卓越)";
            break;
        case 9812055:
            return "M762突击步枪(黑鹰)";
            break;
        case 9812056:
            return "M762突击步枪(铁爪)";
            break;
        case 9812057:
            return "Mk47突击步枪(破损)";
            break;
        case 9812058:
            return "Mk47突击步枪(修复)";
            break;
        case 9812059:
            return "Mk47突击步枪(完好)";
            break;
        case 9812060:
            return "Mk47突击步枪(改进)";
            break;
        case 9812064:
            return "G36C突击步枪(卓越)";
            break;
        case 9812065:
            return "G36C突击步枪(完好)";
            break;
        case 9812066:
            return "G36C突击步枪(改进)";
            break;
        case 9812067:
            return "G36C突击步枪(精制)";
            break;
        case 9812068:
            return "G36C突击步枪(卓越)";
            break;
        case 9812069:
            return "G36C突击步枪(黑鹰)";
            break;
        case 9812070:
            return "G36C突击步枪(铁爪)";
            break;
        case 9812071:
            return "AC-VAL突击步枪(破损)";
            break;
        case 9812072:
            return "AC-VAL突击步枪(修复)";
            break;
        case 9812073:
            return "AC-VAL突击步枪(完好)";
            break;
        case 9812074:
            return "AC-VAL突击步枪(改进)";
            break;
        case 9812078:
            return "蜜獾突击步枪(卓越)";
            break;
        case 9812079:
            return "蜜獾突击步枪(完好)";
            break;
        case 9812080:
            return "蜜獾突击步枪(改进)";
            break;
        case 9812081:
            return "蜜獾突击步枪(精制)";
            break;
        case 9812082:
            return "蜜獾突击步枪(卓越)";
            break;
        case 9812083:
            return "蜜獾突击步枪(黑鹰)";
            break;
        case 9812084:
            return "蜜獾突击步枪(铁爪)";
            break;
        case 9812085:
            return "Famas突击步枪(卓越)";
            break;
        case 9812086:
            return "Famas突击步枪(完好)";
            break;
        case 9812087:
            return "Famas突击步枪(改进)";
            break;
        case 9812088:
            return "Famas突击步枪(精制)";
            break;
        case 9812089:
            return "Famas突击步枪(卓越)";
            break;
        case 9812090:
            return "Famas突击步枪(黑鹰)";
            break;
        case 9812091:
            return "Famas突击步枪(铁爪)";
            break;
        case 9812092:
            return "M416突击步枪·卡德尔";
            break;
        case 9813001:
            return "M249轻机枪(卓越)";
            break;
        case 9813002:
            return "M249轻机枪(改进)";
            break;
        case 9813003:
            return "M249轻机枪(精制)";
            break;
        case 9813004:
            return "M249轻机枪(卓越)";
            break;
        case 9813005:
            return "M249轻机枪(黑鹰)";
            break;
        case 9813006:
            return "M249轻机枪(铁爪)";
            break;
        case 9813008:
            return "DP-28轻机枪(破损)";
            break;
        case 9813009:
            return "DP-28轻机枪(修复)";
            break;
        case 9813010:
            return "DP-28轻机枪(完好)";
            break;
        case 9813022:
            return "MG3轻机枪(卓越)";
            break;
        case 9813023:
            return "MG3轻机枪(改进)";
            break;
        case 9813024:
            return "MG3轻机枪(精制)";
            break;
        case 9813025:
            return "MG3轻机枪(卓越)";
            break;
        case 9813026:
            return "MG3轻机枪(黑鹰)";
            break;
        case 9813027:
            return "MG3轻机枪(铁爪)";
            break;
        case 9813029:
            return "PKM轻机枪(卓越)";
            break;
        case 9813030:
            return "PKM轻机枪(改进)";
            break;
        case 9813031:
            return "PKM轻机枪(精制)";
            break;
        case 9813032:
            return "PKM轻机枪(卓越)";
            break;
        case 9813033:
            return "PKM轻机枪(黑鹰)";
            break;
        case 9813034:
            return "PKM轻机枪(铁爪)";
            break;
        case 9814001:
            return "Kar98K狙击枪(修复)";
            break;
        case 9814002:
            return "Kar98K狙击枪(完好)";
            break;
        case 9814003:
            return "Kar98K狙击枪(改进)";
            break;
        case 9814008:
            return "M24狙击枪(卓越)";
            break;
        case 9814009:
            return "M24狙击枪(完好)";
            break;
        case 9814010:
            return "M24狙击枪(改进)";
            break;
        case 9814011:
            return "M24狙击枪(精制)";
            break;
        case 9814012:
            return "M24狙击枪(卓越)";
            break;
        case 9814013:
            return "M24狙击枪(黑鹰)";
            break;
        case 9814014:
            return "M24狙击枪(铁爪)";
            break;
        case 9814015:
            return "AWM狙击枪(卓越)";
            break;
        case 9814016:
            return "AWM狙击枪(改进)";
            break;
        case 9814017:
            return "AWM狙击枪(精制)";
            break;
        case 9814018:
            return "AWM狙击枪(卓越)";
            break;
        case 9814019:
            return "AWM狙击枪(黑鹰)";
            break;
        case 9814020:
            return "AWM狙击枪(铁爪)";
            break;
        case 9814022:
            return "莫辛纳甘狙击枪(破损)";
            break;
        case 9814023:
            return "莫辛纳甘狙击枪(修复)";
            break;
        case 9814024:
            return "莫辛纳甘狙击枪(完好)";
            break;
        case 9814029:
            return "Win94狙击枪(破损)";
            break;
        case 9814030:
            return "Win94狙击枪(修复)";
            break;
        case 9814031:
            return "Win94狙击枪(完好)";
            break;
        case 9814036:
            return "AMR狙击枪(卓越)";
            break;
        case 9814037:
            return "AMR狙击枪(改进)";
            break;
        case 9814038:
            return "AMR狙击枪(精制)";
            break;
        case 9814039:
            return "AMR狙击枪(卓越)";
            break;
        case 9814040:
            return "AMR狙击枪(黑鹰)";
            break;
        case 9814041:
            return "AMR狙击枪(铁爪)";
            break;
        case 9814043:
            return "M200狙击枪(卓越)";
            break;
        case 9814044:
            return "M200狙击枪(完好)";
            break;
        case 9814045:
            return "M200狙击枪(改进)";
            break;
        case 9814046:
            return "M200狙击枪(精制)";
            break;
        case 9814047:
            return "M200狙击枪(卓越)";
            break;
        case 9814048:
            return "M200狙击枪(黑鹰)";
            break;
        case 9814049:
            return "M200狙击枪(铁爪)";
            break;
        case 9815001:
            return "SKS射手步枪(卓越)";
            break;
        case 9815002:
            return "SKS射手步枪(完好)";
            break;
        case 9815003:
            return "SKS射手步枪(改进)";
            break;
        case 9815004:
            return "SKS射手步枪(精制)";
            break;
        case 9815005:
            return "SKS射手步枪(卓越)";
            break;
        case 9815006:
            return "SKS射手步枪(黑鹰)";
            break;
        case 9815007:
            return "SKS射手步枪(铁爪)";
            break;
        case 9815008:
            return "VSS射手步枪(破损)";
            break;
        case 9815009:
            return "VSS射手步枪(修复)";
            break;
        case 9815010:
            return "VSS射手步枪(完好)";
            break;
        case 9815015:
            return "Mini14射手步枪(破损)";
            break;
        case 9815016:
            return "Mini14射手步枪(修复)";
            break;
        case 9815017:
            return "Mini14射手步枪(完好)";
            break;
        case 9815018:
            return "Mini14射手步枪(改进)";
            break;
        case 9815022:
            return "Mk14射手步枪(卓越)";
            break;
        case 9815023:
            return "Mk14射手步枪(完好)";
            break;
        case 9815024:
            return "Mk14射手步枪(改进)";
            break;
        case 9815025:
            return "Mk14射手步枪(精制)";
            break;
        case 9815026:
            return "Mk14射手步枪(卓越)";
            break;
        case 9815027:
            return "Mk14射手步枪(黑鹰)";
            break;
        case 9815028:
            return "Mk14射手步枪(铁爪)";
            break;
        case 9815029:
            return "SLR射手步枪(卓越)";
            break;
        case 9815030:
            return "SLR射手步枪(完好)";
            break;
        case 9815031:
            return "SLR射手步枪(改进)";
            break;
        case 9815032:
            return "SLR射手步枪(精制)";
            break;
        case 9815033:
            return "SLR射手步枪(卓越)";
            break;
        case 9815034:
            return "SLR射手步枪(黑鹰)";
            break;
        case 9815035:
            return "SLR射手步枪(铁爪)";
            break;
        case 9815036:
            return "QBU射手步枪(破损)";
            break;
        case 9815037:
            return "QBU射手步枪(修复)";
            break;
        case 9815038:
            return "QBU射手步枪(完好)";
            break;
        case 9815039:
            return "QBU射手步枪(改进)";
            break;
        case 9815043:
            return "M417射手步枪(卓越)";
            break;
        case 9815044:
            return "M417射手步枪(完好)";
            break;
        case 9815045:
            return "M417射手步枪(改进)";
            break;
        case 9815046:
            return "M417射手步枪(精制)";
            break;
        case 9815047:
            return "M417射手步枪(卓越)";
            break;
        case 9815048:
            return "M417射手步枪(黑鹰)";
            break;
        case 9815049:
            return "M417射手步枪(铁爪)";
            break;
        case 9815050:
            return "MK20-H射手步枪(卓越)";
            break;
        case 9815051:
            return "MK20-H射手步枪(完好)";
            break;
        case 9815052:
            return "MK20-H射手步枪(改进)";
            break;
        case 9815053:
            return "MK20-H射手步枪(精制)";
            break;
        case 9815054:
            return "MK20-H射手步枪(卓越)";
            break;
        case 9815055:
            return "MK20-H射手步枪(黑鹰)";
            break;
        case 9815056:
            return "MK20-H射手步枪(铁爪)";
            break;
        case 9815064:
            return "MK12射手步枪(卓越)";
            break;
        case 9815065:
            return "MK12射手步枪(完好)";
            break;
        case 9815066:
            return "MK12射手步枪(改进)";
            break;
        case 9815067:
            return "MK12射手步枪(精制)";
            break;
        case 9815068:
            return "MK12射手步枪(卓越)";
            break;
        case 9815069:
            return "MK12射手步枪(黑鹰)";
            break;
        case 9815070:
            return "MK12射手步枪(铁爪)";
            break;
        case 9815071:
            return "M134_装甲重机枪";
            break;
        case 9816001:
            return "钢制插板(破损)";
            break;
        case 9816002:
            return "钢制插板(修复)";
            break;
        case 9816003:
            return "钢制插板(完好)";
            break;
        case 9816004:
            return "钢制插板(改进)";
            break;
        case 9816005:
            return "钢制插板(精制)";
            break;
        case 9816006:
            return "复合纤维插板(破损)";
            break;
        case 9816007:
            return "复合纤维插板(修复)";
            break;
        case 9816008:
            return "复合纤维插板(完好)";
            break;
        case 9816009:
            return "复合纤维插板(改进)";
            break;
        case 9816010:
            return "复合纤维插板(精制)";
            break;
        case 9816011:
            return "内衬外骨骼(破损)";
            break;
        case 9816012:
            return "内衬外骨骼(修复)";
            break;
        case 9816013:
            return "内衬外骨骼(完好)";
            break;
        case 9816014:
            return "内衬外骨骼(改进)";
            break;
        case 9816015:
            return "内衬外骨骼(精制)";
            break;
        case 9816016:
            return "尼龙绑带(破损)";
            break;
        case 9816017:
            return "尼龙绑带(修复)";
            break;
        case 9816018:
            return "尼龙绑带(完好)";
            break;
        case 9816019:
            return "尼龙绑带(改进)";
            break;
        case 9816020:
            return "尼龙绑带(精制)";
            break;
        case 9816021:
            return "战术小包(破损)";
            break;
        case 9816022:
            return "战术小包(修复)";
            break;
        case 9816023:
            return "战术小包(完好)";
            break;
        case 9816024:
            return "战术小包(改进)";
            break;
        case 9816025:
            return "战术小包(精制)";
            break;
        case 9816026:
            return "信号增强器";
            break;
        case 9817001:
            return "红点瞄准镜";
            break;
        case 9817002:
            return "全息瞄准镜";
            break;
        case 9817003:
            return "2倍 瞄准镜";
            break;
        case 9817004:
            return "4倍 瞄准镜";
            break;
        case 9817005:
            return "8倍 瞄准镜";
            break;
        case 9817006:
            return "3倍 瞄准镜";
            break;
        case 9817007:
            return "6倍 瞄准镜";
            break;
        case 9817008:
            return "侧面瞄准镜";
            break;
        case 9817010:
            return "夜视瞄准镜";
            break;
        case 9817011:
            return "热成像瞄准镜";
            break;
        case 9818001:
            return "霰弹枪收束器(破损)";
            break;
        case 9818002:
            return "霰弹枪收束器(修复)";
            break;
        case 9818003:
            return "霰弹枪收束器(完好)";
            break;
        case 9818004:
            return "霰弹枪收束器(改进)";
            break;
        case 9818008:
            return "枪口补偿器(冲锋枪)(修复)";
            break;
        case 9818009:
            return "枪口补偿器(冲锋枪)(完好)";
            break;
        case 9818010:
            return "枪口补偿器(冲锋枪)(改进)";
            break;
        case 9818011:
            return "枪口补偿器(冲锋枪)(精制)";
            break;
        case 9818015:
            return "枪口补偿器(狙击枪)(修复)";
            break;
        case 9818016:
            return "枪口补偿器(狙击枪)(完好)";
            break;
        case 9818017:
            return "枪口补偿器(狙击枪)(改进)";
            break;
        case 9818018:
            return "枪口补偿器(狙击枪)(精制)";
            break;
        case 9818022:
            return "消焰器(冲锋枪)(破损)";
            break;
        case 9818023:
            return "消焰器(冲锋枪)(修复)";
            break;
        case 9818024:
            return "消焰器(冲锋枪)(完好)";
            break;
        case 9818025:
            return "消焰器(冲锋枪)(改进)";
            break;
        case 9818029:
            return "消焰器(狙击枪)(修复)";
            break;
        case 9818030:
            return "消焰器(狙击枪)(完好)";
            break;
        case 9818031:
            return "消焰器(狙击枪)(改进)";
            break;
        case 9818032:
            return "消焰器(狙击枪)(精制)";
            break;
        case 9818036:
            return "消音器(冲锋枪,手枪)(修复)";
            break;
        case 9818037:
            return "消音器(冲锋枪,手枪)(完好)";
            break;
        case 9818038:
            return "消音器(冲锋枪,手枪)(改进)";
            break;
        case 9818039:
            return "消音器(冲锋枪,手枪)(精制)";
            break;
        case 9818043:
            return "消音器(狙击枪)(修复)";
            break;
        case 9818044:
            return "消音器(狙击枪)(完好)";
            break;
        case 9818045:
            return "消音器(狙击枪)(改进)";
            break;
        case 9818046:
            return "消音器(狙击枪)(精制)";
            break;
        case 9818058:
            return "枪口补偿器(步枪)(修复)";
            break;
        case 9818059:
            return "枪口补偿器(步枪)(完好)";
            break;
        case 9818060:
            return "枪口补偿器(步枪)(改进)";
            break;
        case 9818061:
            return "枪口补偿器(步枪)(精制)";
            break;
        case 9818064:
            return "消焰器(步枪)(破损)";
            break;
        case 9818065:
            return "消焰器(步枪)(修复)";
            break;
        case 9818066:
            return "消焰器(步枪)(完好)";
            break;
        case 9818067:
            return "消焰器(步枪)(改进)";
            break;
        case 9818072:
            return "消音器(步枪)(修复)";
            break;
        case 9818073:
            return "消音器(步枪)(完好)";
            break;
        case 9818074:
            return "消音器(步枪)(改进)";
            break;
        case 9818075:
            return "消音器(步枪)(精制)";
            break;
        case 9818078:
            return "鸭嘴枪口(霰弹枪)(破损)";
            break;
        case 9818079:
            return "鸭嘴枪口(霰弹枪)(修复)";
            break;
        case 9818080:
            return "鸭嘴枪口(霰弹枪)(完好)";
            break;
        case 9818081:
            return "鸭嘴枪口(霰弹枪)(改进)";
            break;
        case 9818082:
            return "鸭嘴枪口(霰弹枪)(精制)";
            break;
        case 9818085:
            return "延长枪管(冲锋枪)(破损)";
            break;
        case 9818086:
            return "延长枪管(冲锋枪)(修复)";
            break;
        case 9818087:
            return "延长枪管(冲锋枪)(完好)";
            break;
        case 9818088:
            return "延长枪管(冲锋枪)(改进)";
            break;
        case 9818092:
            return "延长枪管(步枪,狙击枪)(破损)";
            break;
        case 9818093:
            return "延长枪管(步枪,狙击枪)(修复)";
            break;
        case 9818094:
            return "延长枪管(步枪,狙击枪)(完好)";
            break;
        case 9818095:
            return "延长枪管(步枪,狙击枪)(改进)";
            break;
        case 9819001:
            return "扩容弹匣(冲锋枪,手枪)(破损)";
            break;
        case 9819002:
            return "扩容弹匣(冲锋枪,手枪)(修复)";
            break;
        case 9819003:
            return "扩容弹匣(冲锋枪,手枪)(完好)";
            break;
        case 9819004:
            return "扩容弹匣(冲锋枪,手枪)(改进)";
            break;
        case 9819005:
            return "扩容弹匣(冲锋枪,手枪)(精制)";
            break;
        case 9819008:
            return "快速弹匣(冲锋枪,手枪)(破损)";
            break;
        case 9819009:
            return "快速弹匣(冲锋枪,手枪)(修复)";
            break;
        case 9819010:
            return "快速弹匣(冲锋枪,手枪)(完好)";
            break;
        case 9819011:
            return "快速弹匣(冲锋枪,手枪)(改进)";
            break;
        case 9819012:
            return "快速弹匣(冲锋枪,手枪)(精制)";
            break;
        case 9819036:
            return "快速扩容弹匣(冲锋枪,手枪)(修复)";
            break;
        case 9819037:
            return "快速扩容弹匣(冲锋枪,手枪)(完好)";
            break;
        case 9819038:
            return "快速扩容弹匣(冲锋枪,手枪)(改进)";
            break;
        case 9819039:
            return "快速扩容弹匣(冲锋枪,手枪)(精制)";
            break;
        case 9819043:
            return "扩容弹匣(狙击枪)(破损)";
            break;
        case 9819044:
            return "扩容弹匣(狙击枪)(修复)";
            break;
        case 9819045:
            return "扩容弹匣(狙击枪)(完好)";
            break;
        case 9819046:
            return "扩容弹匣(狙击枪)(改进)";
            break;
        case 9819047:
            return "扩容弹匣(狙击枪)(精制)";
            break;
        case 9819050:
            return "快速弹匣(狙击枪)(破损)";
            break;
        case 9819051:
            return "快速弹匣(狙击枪)(修复)";
            break;
        case 9819052:
            return "快速弹匣(狙击枪)(完好)";
            break;
        case 9819053:
            return "快速弹匣(狙击枪)(改进)";
            break;
        case 9819054:
            return "快速弹匣(狙击枪)(精制)";
            break;
        case 9819057:
            return "快速扩容弹匣(狙击枪)(修复)";
            break;
        case 9819058:
            return "快速扩容弹匣(狙击枪)(完好)";
            break;
        case 9819059:
            return "快速扩容弹匣(狙击枪)(改进)";
            break;
        case 9819060:
            return "快速扩容弹匣(狙击枪)(精制)";
            break;
        case 9819071:
            return "扩容弹匣(步枪,机枪)(破损)";
            break;
        case 9819072:
            return "扩容弹匣(步枪,机枪)(修复)";
            break;
        case 9819073:
            return "扩容弹匣(步枪,机枪)(完好)";
            break;
        case 9819074:
            return "扩容弹匣(步枪,机枪)(改进)";
            break;
        case 9819075:
            return "扩容弹匣(步枪,机枪)(精制)";
            break;
        case 9819078:
            return "快速弹匣(步枪,机枪)(破损)";
            break;
        case 9819079:
            return "快速弹匣(步枪,机枪)(修复)";
            break;
        case 9819080:
            return "快速弹匣(步枪,机枪)(完好)";
            break;
        case 9819081:
            return "快速弹匣(步枪,机枪)(改进)";
            break;
        case 9819082:
            return "快速弹匣(步枪,机枪)(精制)";
            break;
        case 9819086:
            return "快速扩容弹匣(步枪,机枪)(修复)";
            break;
        case 9819087:
            return "快速扩容弹匣(步枪,机枪)(完好)";
            break;
        case 9819088:
            return "快速扩容弹匣(步枪,机枪)(改进)";
            break;
        case 9819089:
            return "快速扩容弹匣(步枪,机枪)(精制)";
            break;
        case 9819092:
            return "子弹袋(狙击枪,霰弹枪)(修复)";
            break;
        case 9819093:
            return "子弹袋(狙击枪,霰弹枪)(完好)";
            break;
        case 9819094:
            return "子弹袋(狙击枪,霰弹枪)(改进)";
            break;
        case 9819095:
            return "子弹袋(狙击枪,霰弹枪)(精制)";
            break;
        case 9819106:
            return "霰弹快速装填器";
            break;
        case 9819156:
            return "M134默认弹匣";
            break;
        case 9820002:
            return "战术枪托(修复)";
            break;
        case 9820003:
            return "战术枪托(完好)";
            break;
        case 9820004:
            return "战术枪托(改进)";
            break;
        case 9820005:
            return "战术枪托(精制)";
            break;
        case 9820008:
            return "托腮板(狙击枪)(修复)";
            break;
        case 9820009:
            return "托腮板(狙击枪)(完好)";
            break;
        case 9820010:
            return "托腮板(狙击枪)(改进)";
            break;
        case 9820011:
            return "托腮板(狙击枪)(精制)";
            break;
        case 9820015:
            return "箭袋(十字弩)(破损)";
            break;
        case 9820016:
            return "箭袋(十字弩)(修复)";
            break;
        case 9820017:
            return "箭袋(十字弩)(完好)";
            break;
        case 9820018:
            return "箭袋(十字弩)(改进)";
            break;
        case 9820019:
            return "箭袋(十字弩)(精制)";
            break;
        case 9820022:
            return "撞火枪托";
            break;
        case 9820023:
            return "枪托(Micro UZI)(修复)";
            break;
        case 9820024:
            return "枪托(Micro UZI)(完好)";
            break;
        case 9820025:
            return "枪托(Micro UZI)(改进)";
            break;
        case 9820026:
            return "枪托(Micro UZI)(精制)";
            break;
        case 9821001:
            return "直角前握把(破损)";
            break;
        case 9821002:
            return "直角前握把(修复)";
            break;
        case 9821003:
            return "直角前握把(完好)";
            break;
        case 9821004:
            return "直角前握把(改进)";
            break;
        case 9821005:
            return "直角前握把(精制)";
            break;
        case 9821008:
            return "垂直握把(破损)";
            break;
        case 9821009:
            return "垂直握把(修复)";
            break;
        case 9821010:
            return "垂直握把(完好)";
            break;
        case 9821011:
            return "垂直握把(改进)";
            break;
        case 9821012:
            return "垂直握把(精制)";
            break;
        case 9821015:
            return "轻型握把(破损)";
            break;
        case 9821016:
            return "轻型握把(修复)";
            break;
        case 9821017:
            return "轻型握把(完好)";
            break;
        case 9821018:
            return "轻型握把(改进)";
            break;
        case 9821019:
            return "轻型握把(精制)";
            break;
        case 9821022:
            return "半截式握把(破损)";
            break;
        case 9821023:
            return "半截式握把(修复)";
            break;
        case 9821024:
            return "半截式握把(完好)";
            break;
        case 9821025:
            return "半截式握把(改进)";
            break;
        case 9821026:
            return "半截式握把(精制)";
            break;
        case 9821029:
            return "拇指握把(破损)";
            break;
        case 9821030:
            return "拇指握把(修复)";
            break;
        case 9821031:
            return "拇指握把(完好)";
            break;
        case 9821032:
            return "拇指握把(改进)";
            break;
        case 9821033:
            return "拇指握把(精制)";
            break;
        case 9821036:
            return "激光瞄准器(破损)";
            break;
        case 9821037:
            return "激光瞄准器(修复)";
            break;
        case 9821038:
            return "激光瞄准器(完好)";
            break;
        case 9821039:
            return "激光瞄准器(改进)";
            break;
        case 9821040:
            return "激光瞄准器(精制)";
            break;
        case 9821044:
            return "下挂榴弹发射器";
            break;
        case 9821045:
            return "下挂喷火器";
            break;
        case 9823001:
            return "9毫米子弹(锈蚀)";
            break;
        case 9823002:
            return "9毫米子弹(普通)";
            break;
        case 9823003:
            return "9毫米子弹(抛光)";
            break;
        case 9823004:
            return "9毫米子弹(高爆)";
            break;
        case 9823005:
            return "9毫米子弹(爆燃)";
            break;
        case 9823006:
            return "9毫米子弹(剧毒)";
            break;
        case 9823007:
            return "5.7毫米子弹(锈蚀)";
            break;
        case 9823008:
            return "5.7毫米子弹(普通)";
            break;
        case 9823009:
            return "5.7毫米子弹(抛光)";
            break;
        case 9823010:
            return "5.7毫米子弹(高爆)";
            break;
        case 9823011:
            return "5.7毫米子弹(爆燃)";
            break;
        case 9823012:
            return "5.7毫米子弹(剧毒)";
            break;
        case 9823013:
            return "7.62毫米子弹(锈蚀)";
            break;
        case 9823014:
            return "7.62毫米子弹(普通)";
            break;
        case 9823015:
            return "7.62毫米子弹(抛光)";
            break;
        case 9823016:
            return "7.62毫米子弹(高爆)";
            break;
        case 9823017:
            return "7.62毫米子弹(爆燃)";
            break;
        case 9823018:
            return "7.62毫米子弹(剧毒)";
            break;
        case 9823019:
            return "5.56毫米子弹(锈蚀)";
            break;
        case 9823020:
            return "5.56毫米子弹(普通)";
            break;
        case 9823021:
            return "5.56毫米子弹(抛光)";
            break;
        case 9823022:
            return "5.56毫米子弹(高爆)";
            break;
        case 9823023:
            return "5.56毫米子弹(爆燃)";
            break;
        case 9823024:
            return "5.56毫米子弹(剧毒)";
            break;
        case 9823025:
            return "12口径霰弹(锈蚀)";
            break;
        case 9823026:
            return "12口径霰弹(普通)";
            break;
        case 9823027:
            return "12口径霰弹(抛光)";
            break;
        case 9823028:
            return "12口径霰弹(高爆)";
            break;
        case 9823029:
            return "12口径霰弹(爆燃)";
            break;
        case 9823030:
            return "12口径霰弹(剧毒)";
            break;
        case 9823031:
            return ".45口径子弹(锈蚀)";
            break;
        case 9823032:
            return ".45口径子弹(普通)";
            break;
        case 9823033:
            return ".45口径子弹(抛光)";
            break;
        case 9823034:
            return ".45口径子弹(高爆)";
            break;
        case 9823035:
            return ".45口径子弹(爆燃)";
            break;
        case 9823036:
            return ".45口径子弹(剧毒)";
            break;
        case 9823037:
            return ".300马格南子弹(锈蚀)";
            break;
        case 9823038:
            return ".300马格南子弹(普通)";
            break;
        case 9823039:
            return ".300马格南子弹(抛光)";
            break;
        case 9823040:
            return ".300马格南子弹(高爆)";
            break;
        case 9823041:
            return ".300马格南子弹(爆燃)";
            break;
        case 9823042:
            return ".300马格南子弹(剧毒)";
            break;
        case 9823043:
            return ".50口径子弹(锈蚀)";
            break;
        case 9823044:
            return ".50口径子弹(普通)";
            break;
        case 9823045:
            return ".50口径子弹(抛光)";
            break;
        case 9823046:
            return ".50口径子弹(高爆)";
            break;
        case 9823047:
            return ".50口径子弹(爆燃)";
            break;
        case 9823048:
            return ".50口径子弹(剧毒)";
            break;
        case 9823049:
            return ".408口径子弹(锈蚀)";
            break;
        case 9823050:
            return ".408口径子弹(普通)";
            break;
        case 9823051:
            return ".408口径子弹(抛光)";
            break;
        case 9823052:
            return ".408口径子弹(高爆)";
            break;
        case 9823053:
            return ".408口径子弹(爆燃)";
            break;
        case 9823054:
            return ".408口径子弹(剧毒)";
            break;
        case 9823060:
            return "装甲加特林子弹";
            break;
        case 9823061:
            return "40mm榴弹";
            break;
        case 9823062:
            return "火箭弹";
            break;
        case 9823063:
            return "燃气瓶";
            break;
        case 9823064:
            return "9毫米子弹(竞赛)";
            break;
        case 9823065:
            return "5.7毫米子弹(竞赛)";
            break;
        case 9823066:
            return "7.62毫米子弹(竞赛)";
            break;
        case 9823067:
            return "5.56毫米子弹(竞赛)";
            break;
        case 9823068:
            return "12口径霰弹(竞赛)";
            break;
        case 9823069:
            return ".45口径子弹(竞赛)";
            break;
        case 9823070:
            return ".300马格南子弹(竞赛)";
            break;
        case 9823071:
            return ".50口径子弹(竞赛)";
            break;
        case 9823072:
            return ".408口径子弹(竞赛)";
            break;
        case 9823073:
            return "箭矢(锈蚀)";
            break;
        case 9823074:
            return "箭矢(普通)";
            break;
        case 9823075:
            return "箭矢(抛光)";
            break;
        case 9823076:
            return "箭矢(高爆)";
            break;
        case 9823077:
            return "箭矢(爆燃)";
            break;
        case 9823078:
            return "箭矢(剧毒)";
            break;
        case 9823079:
            return "箭矢(竞赛)";
            break;
        case 9823080:
            return "E玩法信号弹";
            break;
        case 9824001:
            return "能量饮料";
            break;
        case 9824002:
            return "肾上腺素";
            break;
        case 9824003:
            return "止痛药";
            break;
        case 9824004:
            return "绷带";
            break;
        case 9824005:
            return "急救包";
            break;
        case 9824006:
            return "全能医疗箱";
            break;
        case 9824007:
            return "抗干扰药";
            break;
        case 9825001:
            return "震爆弹";
            break;
        case 9825002:
            return "烟雾弹";
            break;
        case 9825003:
            return "燃烧瓶";
            break;
        case 9825004:
            return "破片手榴弹";
            break;
        case 9826001:
            return "液氮地雷";
            break;
        case 9826002:
            return "雷鸣地雷";
            break;
        case 9826003:
            return "自救型除颤器";
            break;
        case 9826004:
            return "便携式生命探测仪";
            break;
        case 9826005:
            return "弹道追踪仪";
            break;
        case 9828001:
            return "RPG-7火箭筒";
            break;
        case 9828002:
            return "M79榴弹发射器";
            break;
        case 9828003:
            return "突击盾牌";
            break;
        default:
            return "NULL";
        break;
    }
}

inline int heldconversion(int BoxId)
{
  switch (BoxId)
  {
    case 9808001:
case 9808002:
case 9808003:
    return 106001;
    break;
case 9808008:
case 9808009:
case 9808010:
    return 106002;
    break;
case 9808015:
case 9808016:
case 9808017:
    return 106003;
    break;
case 9808022:
case 9808023:
case 9808024:
    return 106004;
    break;
case 9808029:
case 9808030:
case 9808031:
    return 106005;
    break;
case 9808036:
case 9808037:
case 9808038:
    return 106006;
    break;
case 9808043:
case 9808044:
case 9808045:
case 9808046:
    return 106008;
    break;
case 9808050:
case 9808051:
case 9808052:
case 9808053:
    return 106010;
    break;
case 9808057:
case 9808058:
case 9808059:
case 9808061:
    return 106011;
    break;
case 9809001:
case 9809002:
case 9809003:
    return 107001;
    break;
case 9809009:
case 9809010:
    return 107007;
    break;
case 9810001:
case 9810002:
case 9810003:
    return 104001;
    break;
case 9810008:
case 9810009:
case 9810010:
    return 104002;
    break;
case 9810015:
case 9810016:
case 9810017:
case 9810018:
    return 104003;
    break;
case 9810022:
case 9810023:
case 9810024:
case 9810025:
    return 104004;
    break;
case 9810029:
case 9810030:
case 9810031:
case 9810032:
    return 104100;
    break;
case 9810036:
case 9810037:
case 9810038:
case 9810041:
    return 104005;
    break;
case 9811001:
case 9811002:
case 9811003:
case 9811004:
    return 102001;
    break;
case 9811008:
case 9811009:
case 9811010:
case 9811011:
    return 102002;
    break;
case 9811015:
case 9811016:
case 9811017:
case 9811018:
case 9811019:
case 9811020:
case 9811021:
    return 102003;
    break;
case 9811022:
case 9811023:
case 9811024:
case 9811025:
    return 102004;
    break;
case 9811029:
case 9811030:
case 9811031:
    return 102005;
    break;
case 9811036:
case 9811037:
case 9811038:
case 9811039:
case 9811040:
case 9811041:
case 9811042:
    return 102007;
    break;
case 9811043:
case 9811044:
case 9811045:
case 9811046:
case 9811047:
case 9811048:
case 9811049:
    return 102008;
    break;
case 9811050:
case 9811051:
case 9811052:
case 9811053:
case 9811054:
case 9811055:
case 9811056:
    return 102105;
    break;
case 9812002:
case 9812003:
case 9812004:
case 9812005:
case 9812006:
case 9812007:
case 9812008:
    return 101001;
    break;
case 9812009:
case 9812010:
case 9812011:
case 9812012:
    return 101002;
    break;
case 9812015:
case 9812016:
case 9812017:
case 9812018:
case 9812019:
case 9812020:
case 9812021:
    return 101003;
    break;
case 9812022:
case 9812023:
case 9812024:
case 9812025:
case 9812026:
case 9812027:
case 9812028:
case 9812092:
    return 101004;
    break;
case 9812029:
case 9812030:
case 9812031:
case 9812032:
case 9812033:
case 9812034:
    return 101005;
    break;
case 9812036:
case 9812037:
case 9812038:
case 9812039:
case 9812040:
case 9812041:
    return 101006;
    break;
case 9812043:
case 9812044:
case 9812045:
case 9812046:
case 9812047:
case 9812048:
case 9812049:
    return 101007;
    break;
case 9812050:
case 9812051:
case 9812052:
case 9812053:
case 9812054:
case 9812055:
case 9812056:
    return 101008;
    break;
case 9812057:
case 9812058:
case 9812059:
case 9812060:
    return 101009;
    break;
case 9812064:
case 9812065:
case 9812066:
case 9812067:
case 9812068:
case 9812069:
case 9812070:
    return 101010;
    break;
case 9812071:
case 9812072:
case 9812073:
case 9812074:
    return 101011;
    break;
case 9812078:
case 9812079:
case 9812080:
case 9812081:
case 9812082:
case 9812083:
case 9812084:
    return 101012;
    break;
case 9812085:
case 9812086:
case 9812087:
case 9812088:
case 9812089:
case 9812090:
case 9812091:
    return 101013;
    break;
case 9813001:
case 9813002:
case 9813003:
case 9813004:
case 9813005:
case 9813006:
    return 105001;
    break;
case 9813008:
case 9813009:
case 9813010:
    return 105002;
    break;
case 9813022:
case 9813023:
case 9813024:
case 9813025:
case 9813026:
case 9813027:
    return 105010;
    break;
case 9813029:
case 9813030:
case 9813031:
case 9813032:
case 9813033:
case 9813034:
    return 105012;
    break;
case 9814001:
case 9814002:
case 9814003:
    return 103001;
    break;
case 9814008:
case 9814009:
case 9814010:
case 9814011:
case 9814012:
case 9814013:
case 9814014:
    return 103002;
    break;
case 9814015:
case 9814016:
case 9814017:
case 9814018:
case 9814019:
case 9814020:
    return 103003;
    break;
case 9814022:
case 9814023:
case 9814024:
    return 103011;
    break;
case 9814029:
case 9814030:
case 9814031:
    return 103008;
    break;
case 9814036:
case 9814037:
case 9814038:
case 9814039:
case 9814040:
case 9814041:
    return 103012;
    break;
case 9814043:
case 9814044:
case 9814045:
case 9814046:
case 9814047:
case 9814048:
case 9814049:
    return 103015;
    break;
case 9814050:
case 9814051:
case 9814052:
case 9814053:
    return 103016;
    break;
case 9815001:
case 9815002:
case 9815003:
case 9815004:
case 9815005:
case 9815006:
case 9815007:
    return 103004;
    break;
case 9815008:
case 9815009:
case 9815010:
    return 103005;
    break;
case 9815015:
case 9815016:
case 9815017:
case 9815018:
    return 103006;
    break;
case 9815022:
case 9815023:
case 9815024:
case 9815025:
case 9815026:
case 9815027:
case 9815028:
    return 103007;
    break;
case 9815029:
case 9815030:
case 9815031:
case 9815032:
case 9815033:
case 9815034:
case 9815035:
    return 103009;
    break;
case 9815036:
case 9815037:
case 9815038:
case 9815039:
    return 103010;
    break;
case 9815043:
case 9815044:
case 9815045:
case 9815046:
case 9815047:
case 9815048:
case 9815049:
    return 103013;
    break;
case 9815050:
case 9815051:
case 9815052:
case 9815053:
case 9815054:
case 9815055:
case 9815056:
    return 103014;
    break;
case 9815064:
case 9815065:
case 9815066:
case 9815067:
case 9815068:
case 9815069:
case 9815070:
    return 103100;
    break;
    default:
            return BoxId;
        break;
  }
}


inline char *GetHolGunItem(int BoxId)
{
    switch (BoxId)
    {
    
    case 9808001:
    return "P92手枪(破损)";
    break;
case 9808002:
    return "P92手枪(修复)";
    break;
case 9808003:
    return "P92手枪(完好)";
    break;
case 9808008:
    return "P1911手枪(破损)";
    break;
case 9808009:
    return "P1911手枪(修复)";
    break;
case 9808010:
    return "P1911手枪(完好)";
    break;
case 9808015:
    return "R1895手枪(破损)";
    break;
case 9808016:
    return "R1895手枪(修复)";
    break;
case 9808017:
    return "R1895手枪(完好)";
    break;
case 9808022:
    return "P18C手枪(破损)";
    break;
case 9808023:
    return "P18C手枪(修复)";
    break;
case 9808024:
    return "P18C手枪(完好)";
    break;
case 9808029:
    return "R45手枪(破损)";
    break;
case 9808030:
    return "R45手枪(修复)";
    break;
case 9808031:
    return "R45手枪(完好)";
    break;
case 9808036:
    return "短管霰弹枪(破损)";
    break;
case 9808037:
    return "短管霰弹枪(修复)";
    break;
case 9808038:
    return "短管霰弹枪(完好)";
    break;
case 9808043:
    return "蝎式手枪(修复)";
    break;
case 9808044:
    return "蝎式手枪(完好)";
    break;
case 9808045:
    return "蝎式手枪(改进)";
    break;
case 9808046:
    return "蝎式手枪(破损)";
    break;
case 9808050:
    return "沙漠之鹰手枪(修复)";
    break;
case 9808051:
    return "沙漠之鹰手枪(完好)";
    break;
case 9808052:
    return "沙漠之鹰手枪(改进)";
    break;
case 9808053:
    return "沙漠之鹰手枪(破损)";
    break;
case 9808057:
    return "TMP-9手枪(修复)";
    break;
case 9808058:
    return "TMP-9手枪(完好)";
    break;
case 9808059:
    return "TMP-9手枪(改进)";
    break;
case 9808060:
    return "空投信号枪";
    break;
case 9808061:
    return "TMP-9手枪(破损)";
    break;
case 9809001:
    return "十字弩(破损)";
    break;
case 9809002:
    return "十字弩(修复)";
    break;
case 9809003:
    return "十字弩(完好)";
    break;
case 9809009:
    return "爆炸猎弓(骑警Boss)";
    break;
case 9809010:
    return "爆炸猎弓·杰西";
    break;
case 9810001:
    return "S686霰弹枪(破损)";
    break;
case 9810002:
    return "S686霰弹枪(修复)";
    break;
case 9810003:
    return "S686霰弹枪(完好)";
    break;
case 9810008:
    return "S1897霰弹枪(破损)";
    break;
case 9810009:
    return "S1897霰弹枪(修复)";
    break;
case 9810010:
    return "S1897霰弹枪(完好)";
    break;
case 9810015:
    return "S12K霰弹枪(精制)";
    break;
case 9810016:
    return "S12K霰弹枪(修复)";
    break;
case 9810017:
    return "S12K霰弹枪(完好)";
    break;
case 9810018:
    return "S12K霰弹枪(改进)";
    break;
case 9810022:
    return "DBS霰弹枪(修复)";
    break;
case 9810023:
    return "DBS霰弹枪(完好)";
    break;
case 9810024:
    return "DBS霰弹枪(改进)";
    break;
case 9810025:
    return "DBS霰弹枪(精制)";
    break;
case 9810029:
    return "SPAS-12霰弹枪(修复)";
    break;
case 9810030:
    return "SPAS-12霰弹枪(完好)";
    break;
case 9810031:
    return "SPAS-12霰弹枪(改进)";
    break;
case 9810032:
    return "SPAS-12霰弹枪(精制)";
    break;
case 9810036:
    return "AA12-G霰弹枪(修复)";
    break;
case 9810037:
    return "AA12-G霰弹枪(完好)";
    break;
case 9810038:
    return "AA12-G霰弹枪(改进)";
    break;
case 9810041:
    return "AA12-G霰弹枪(精制)";
    break;
case 9811001:
    return "UZI冲锋枪(破损)";
    break;
case 9811002:
    return "UZI冲锋枪(修复)";
    break;
case 9811003:
    return "UZI冲锋枪(完好)";
    break;
case 9811004:
    return "UZI冲锋枪(改进)";
    break;
case 9811008:
    return "UMP45冲锋枪(破损)";
    break;
case 9811009:
    return "UMP45冲锋枪(修复)";
    break;
case 9811010:
    return "UMP45冲锋枪(完好)";
    break;
case 9811011:
    return "UMP45冲锋枪(改进)";
    break;
case 9811015:
    return "Vector冲锋枪(卓越)";
    break;
case 9811016:
    return "Vector冲锋枪(完好)";
    break;
case 9811017:
    return "Vector冲锋枪(改进)";
    break;
case 9811018:
    return "Vector冲锋枪(精制)";
    break;
case 9811019:
    return "Vector冲锋枪(卓越)";
    break;
case 9811020:
    return "Vector冲锋枪(黑鹰)";
    break;
case 9811021:
    return "Vector冲锋枪(铁爪)";
    break;
case 9811022:
    return "汤姆逊冲锋枪(破损)";
    break;
case 9811023:
    return "汤姆逊冲锋枪(修复)";
    break;
case 9811024:
    return "汤姆逊冲锋枪(完好)";
    break;
case 9811025:
    return "汤姆逊冲锋枪(改进)";
    break;
case 9811029:
    return "野牛冲锋枪(破损)";
    break;
case 9811030:
    return "野牛冲锋枪(修复)";
    break;
case 9811031:
    return "野牛冲锋枪(完好)";
    break;
case 9811036:
    return "MP5K冲锋枪(卓越)";
    break;
case 9811037:
    return "MP5K冲锋枪(完好)";
    break;
case 9811038:
    return "MP5K冲锋枪(改进)";
    break;
case 9811039:
    return "MP5K冲锋枪(精制)";
    break;
case 9811040:
    return "MP5K冲锋枪(卓越)";
    break;
case 9811041:
    return "MP5K冲锋枪(黑鹰)";
    break;
case 9811042:
    return "MP5K冲锋枪(铁爪)";
    break;
case 9811043:
    return "AKS-74U冲锋枪(卓越)";
    break;
case 9811044:
    return "AKS-74U冲锋枪(完好)";
    break;
case 9811045:
    return "AKS-74U冲锋枪(改进)";
    break;
case 9811046:
    return "AKS-74U冲锋枪(精制)";
    break;
case 9811047:
    return "AKS-74U冲锋枪(卓越)";
    break;
case 9811048:
    return "AKS-74U冲锋枪(铁爪)";
    break;
case 9811049:
    return "AKS-74U冲锋枪(黑鹰)";
    break;
case 9811050:
    return "P90冲锋枪(卓越)";
    break;
case 9811051:
    return "P90冲锋枪(完好)";
    break;
case 9811052:
    return "P90冲锋枪(改进)";
    break;
case 9811053:
    return "P90冲锋枪(精制)";
    break;
case 9811054:
    return "P90冲锋枪(卓越)";
    break;
case 9811055:
    return "P90冲锋枪(黑鹰)";
    break;
case 9811056:
    return "P90冲锋枪(铁爪)";
    break;
case 9812002:
    return "AKM突击步枪(卓越)";
    break;
case 9812003:
    return "AKM突击步枪(完好)";
    break;
case 9812004:
    return "AKM突击步枪(改进)";
    break;
case 9812005:
    return "AKM突击步枪(精制)";
    break;
case 9812006:
    return "AKM突击步枪(卓越)";
    break;
case 9812007:
    return "AKM突击步枪(黑鹰)";
    break;
case 9812008:
    return "AKM突击步枪(铁爪)";
    break;
case 9812009:
    return "M16A4突击步枪(破损)";
    break;
case 9812010:
    return "M16A4突击步枪(修复)";
    break;
case 9812011:
    return "M16A4突击步枪(完好)";
    break;
case 9812012:
    return "M16A4突击步枪(改进)";
    break;
case 9812015:
    return "SCAR-L突击步枪(卓越)";
    break;
case 9812016:
    return "SCAR-L突击步枪(完好)";
    break;
case 9812017:
    return "SCAR-L突击步枪(改进)";
    break;
case 9812018:
    return "SCAR-L突击步枪(精制)";
    break;
case 9812019:
    return "SCAR-L突击步枪(卓越)";
    break;
case 9812020:
    return "SCAR-L突击步枪(黑鹰)";
    break;
case 9812021:
    return "SCAR-L突击步枪(铁爪)";
    break;
case 9812022:
    return "M416突击步枪(铁爪)";
    break;
case 9812023:
    return "M416突击步枪(卓越)";
    break;
case 9812024:
    return "M416突击步枪(完好)";
    break;
case 9812025:
    return "M416突击步枪(改进)";
    break;
case 9812026:
    return "M416突击步枪(精制)";
    break;
case 9812027:
    return "M416突击步枪(卓越)";
    break;
case 9812028:
    return "M416突击步枪(黑鹰)";
    break;
case 9812029:
    return "GROZA突击步枪(卓越)";
    break;
case 9812030:
    return "GROZA突击步枪(改进)";
    break;
case 9812031:
    return "GROZA突击步枪(精制)";
    break;
case 9812032:
    return "GROZA突击步枪(卓越)";
    break;
case 9812033:
    return "GROZA突击步枪(黑鹰)";
    break;
case 9812034:
    return "GROZA突击步枪(铁爪)";
    break;
case 9812036:
    return "AUG突击步枪(卓越)";
    break;
case 9812037:
    return "AUG突击步枪(改进)";
    break;
case 9812038:
    return "AUG突击步枪(精制)";
    break;
case 9812039:
    return "AUG突击步枪(卓越)";
    break;
case 9812040:
    return "AUG突击步枪(黑鹰)";
    break;
case 9812041:
    return "AUG突击步枪(铁爪)";
    break;
case 9812043:
    return "QBZ突击步枪(卓越)";
    break;
case 9812044:
    return "QBZ突击步枪(完好)";
    break;
case 9812045:
    return "QBZ突击步枪(改进)";
    break;
case 9812046:
    return "QBZ突击步枪(精制)";
    break;
case 9812047:
    return "QBZ突击步枪(卓越)";
    break;
case 9812048:
    return "QBZ突击步枪(黑鹰)";
    break;
case 9812049:
    return "QBZ突击步枪(铁爪)";
    break;
case 9812050:
    return "M762突击步枪(卓越)";
    break;
case 9812051:
    return "M762突击步枪(完好)";
    break;
case 9812052:
    return "M762突击步枪(改进)";
    break;
case 9812053:
    return "M762突击步枪(精制)";
    break;
case 9812054:
    return "M762突击步枪(卓越)";
    break;
case 9812055:
    return "M762突击步枪(黑鹰)";
    break;
case 9812056:
    return "M762突击步枪(铁爪)";
    break;
case 9812057:
    return "Mk47突击步枪(破损)";
    break;
case 9812058:
    return "Mk47突击步枪(修复)";
    break;
case 9812059:
    return "Mk47突击步枪(完好)";
    break;
case 9812060:
    return "Mk47突击步枪(改进)";
    break;
case 9812064:
    return "G36C突击步枪(卓越)";
    break;
case 9812065:
    return "G36C突击步枪(完好)";
    break;
case 9812066:
    return "G36C突击步枪(改进)";
    break;
case 9812067:
    return "G36C突击步枪(精制)";
    break;
case 9812068:
    return "G36C突击步枪(卓越)";
    break;
case 9812069:
    return "G36C突击步枪(黑鹰)";
    break;
case 9812070:
    return "G36C突击步枪(铁爪)";
    break;
case 9812071:
    return "AC-VAL突击步枪(破损)";
    break;
case 9812072:
    return "AC-VAL突击步枪(修复)";
    break;
case 9812073:
    return "AC-VAL突击步枪(完好)";
    break;
case 9812074:
    return "AC-VAL突击步枪(改进)";
    break;
case 9812078:
    return "蜜獾突击步枪(卓越)";
    break;
case 9812079:
    return "蜜獾突击步枪(完好)";
    break;
case 9812080:
    return "蜜獾突击步枪(改进)";
    break;
case 9812081:
    return "蜜獾突击步枪(精制)";
    break;
case 9812082:
    return "蜜獾突击步枪(卓越)";
    break;
case 9812083:
    return "蜜獾突击步枪(黑鹰)";
    break;
case 9812084:
    return "蜜獾突击步枪(铁爪)";
    break;
case 9812085:
    return "FAMAS突击步枪(卓越)";
    break;
case 9812086:
    return "FAMAS突击步枪(完好)";
    break;
case 9812087:
    return "FAMAS突击步枪(改进)";
    break;
case 9812088:
    return "FAMAS突击步枪(精制)";
    break;
case 9812089:
    return "FAMAS突击步枪(卓越)";
    break;
case 9812090:
    return "FAMAS突击步枪(黑鹰)";
    break;
case 9812091:
    return "FAMAS突击步枪(铁爪)";
    break;
case 9812092:
    return "M416突击步枪·卡德尔";
    break;
case 9813001:
    return "M249轻机枪(卓越)";
    break;
case 9813002:
    return "M249轻机枪(改进)";
    break;
case 9813003:
    return "M249轻机枪(精制)";
    break;
case 9813004:
    return "M249轻机枪(卓越)";
    break;
case 9813005:
    return "M249轻机枪(黑鹰)";
    break;
case 9813006:
    return "M249轻机枪(铁爪)";
    break;
case 9813008:
    return "DP-28轻机枪(破损)";
    break;
case 9813009:
    return "DP-28轻机枪(修复)";
    break;
case 9813010:
    return "DP-28轻机枪(完好)";
    break;
case 9813022:
    return "MG3轻机枪(卓越)";
    break;
case 9813023:
    return "MG3轻机枪(改进)";
    break;
case 9813024:
    return "MG3轻机枪(精制)";
    break;
case 9813025:
    return "MG3轻机枪(卓越)";
    break;
case 9813026:
    return "MG3轻机枪(黑鹰)";
    break;
case 9813027:
    return "MG3轻机枪(铁爪)";
    break;
case 9813029:
    return "PKM轻机枪(卓越)";
    break;
case 9813030:
    return "PKM轻机枪(改进)";
    break;
case 9813031:
    return "PKM轻机枪(精制)";
    break;
case 9813032:
    return "PKM轻机枪(卓越)";
    break;
case 9813033:
    return "PKM轻机枪(黑鹰)";
    break;
case 9813034:
    return "PKM轻机枪(铁爪)";
    break;
case 9814001:
    return "Kar98K狙击枪(修复)";
    break;
case 9814002:
    return "Kar98K狙击枪(完好)";
    break;
case 9814003:
    return "Kar98K狙击枪(改进)";
    break;
case 9814008:
    return "M24狙击枪(卓越)";
    break;
case 9814009:
    return "M24狙击枪(完好)";
    break;
case 9814010:
    return "M24狙击枪(改进)";
    break;
case 9814011:
    return "M24狙击枪(精制)";
    break;
case 9814012:
    return "M24狙击枪(卓越)";
    break;
case 9814013:
    return "M24狙击枪(黑鹰)";
    break;
case 9814014:
    return "M24狙击枪(铁爪)";
    break;
case 9814015:
    return "AWM狙击枪(卓越)";
    break;
case 9814016:
    return "AWM狙击枪(改进)";
    break;
case 9814017:
    return "AWM狙击枪(精制)";
    break;
case 9814018:
    return "AWM狙击枪(卓越)";
    break;
case 9814019:
    return "AWM狙击枪(黑鹰)";
    break;
case 9814020:
    return "AWM狙击枪(铁爪)";
    break;
case 9814022:
    return "莫辛纳甘狙击枪(破损)";
    break;
case 9814023:
    return "莫辛纳甘狙击枪(修复)";
    break;
case 9814024:
    return "莫辛纳甘狙击枪(完好)";
    break;
case 9814029:
    return "Win94狙击枪(破损)";
    break;
case 9814030:
    return "Win94狙击枪(修复)";
    break;
case 9814031:
    return "Win94狙击枪(完好)";
    break;
case 9814036:
    return "AMR狙击枪(卓越)";
    break;
case 9814037:
    return "AMR狙击枪(改进)";
    break;
case 9814038:
    return "AMR狙击枪(精制)";
    break;
case 9814039:
    return "AMR狙击枪(卓越)";
    break;
case 9814040:
    return "AMR狙击枪(黑鹰)";
    break;
case 9814041:
    return "AMR狙击枪(铁爪)";
    break;
case 9814043:
    return "M200狙击枪(卓越)";
    break;
case 9814044:
    return "M200狙击枪(完好)";
    break;
case 9814045:
    return "M200狙击枪(改进)";
    break;
case 9814046:
    return "M200狙击枪(精制)";
    break;
case 9814047:
    return "M200狙击枪(卓越)";
    break;
case 9814048:
    return "M200狙击枪(黑鹰)";
    break;
case 9814049:
    return "M200狙击枪(铁爪)";
    break;
case 9814050:
    return "SVD狙击枪(改进)";
    break;
case 9814051:
    return "SVD狙击枪(精制)";
    break;
case 9814052:
    return "SVD狙击枪(卓越)";
    break;
case 9814053:
    return "SVD狙击枪(卓越)";
    break;
case 9815001:
    return "SKS射手步枪(卓越)";
    break;
case 9815002:
    return "SKS射手步枪(完好)";
    break;
case 9815003:
    return "SKS射手步枪(改进)";
    break;
case 9815004:
    return "SKS射手步枪(精制)";
    break;
case 9815005:
    return "SKS射手步枪(卓越)";
    break;
case 9815006:
    return "SKS射手步枪(黑鹰)";
    break;
case 9815007:
    return "SKS射手步枪(铁爪)";
    break;
case 9815008:
    return "VSS射手步枪(破损)";
    break;
case 9815009:
    return "VSS射手步枪(修复)";
    break;
case 9815010:
    return "VSS射手步枪(完好)";
    break;
case 9815015:
    return "Mini14射手步枪(破损)";
    break;
case 9815016:
    return "Mini14射手步枪(修复)";
    break;
case 9815017:
    return "Mini14射手步枪(完好)";
    break;
case 9815018:
    return "Mini14射手步枪(改进)";
    break;
case 9815022:
    return "Mk14射手步枪(卓越)";
    break;
case 9815023:
    return "Mk14射手步枪(完好)";
    break;
case 9815024:
    return "Mk14射手步枪(改进)";
    break;
case 9815025:
    return "Mk14射手步枪(精制)";
    break;
case 9815026:
    return "Mk14射手步枪(卓越)";
    break;
case 9815027:
    return "Mk14射手步枪(黑鹰)";
    break;
case 9815028:
    return "Mk14射手步枪(铁爪)";
    break;
case 9815029:
    return "SLR射手步枪(卓越)";
    break;
case 9815030:
    return "SLR射手步枪(完好)";
    break;
case 9815031:
    return "SLR射手步枪(改进)";
    break;
case 9815032:
    return "SLR射手步枪(精制)";
    break;
case 9815033:
    return "SLR射手步枪(卓越)";
    break;
case 9815034:
    return "SLR射手步枪(黑鹰)";
    break;
case 9815035:
    return "SLR射手步枪(铁爪)";
    break;
case 9815036:
    return "QBU射手步枪(破损)";
    break;
case 9815037:
    return "QBU射手步枪(修复)";
    break;
case 9815038:
    return "QBU射手步枪(完好)";
    break;
case 9815039:
    return "QBU射手步枪(改进)";
    break;
case 9815043:
    return "M417射手步枪(卓越)";
    break;
case 9815044:
    return "M417射手步枪(完好)";
    break;
case 9815045:
    return "M417射手步枪(改进)";
    break;
case 9815046:
    return "M417射手步枪(精制)";
    break;
case 9815047:
    return "M417射手步枪(卓越)";
    break;
case 9815048:
    return "M417射手步枪(黑鹰)";
    break;
case 9815049:
    return "M417射手步枪(铁爪)";
    break;
case 9815050:
    return "MK20-H射手步枪(卓越)";
    break;
case 9815051:
    return "MK20-H射手步枪(完好)";
    break;
case 9815052:
    return "MK20-H射手步枪(改进)";
    break;
case 9815053:
    return "MK20-H射手步枪(精制)";
    break;
case 9815054:
    return "MK20-H射手步枪(卓越)";
    break;
case 9815055:
    return "MK20-H射手步枪(黑鹰)";
    break;
case 9815056:
    return "MK20-H射手步枪(铁爪)";
    break;
case 9815064:
    return "MK12射手步枪(卓越)";
    break;
case 9815065:
    return "MK12射手步枪(完好)";
    break;
case 9815066:
    return "MK12射手步枪(改进)";
    break;
case 9815067:
    return "MK12射手步枪(精制)";
    break;
case 9815068:
    return "MK12射手步枪(卓越)";
    break;
case 9815069:
    return "MK12射手步枪(黑鹰)";
    break;
case 9815070:
    return "MK12射手步枪(铁爪)";
    break;
        //突击步枪
        case 101008:
            return "M762突击步枪";
        break;
        case 101013:
            return "FAMAS突击步枪";
        break;
        case 101001:
            return "AKM突击步枪";
        break;
        case 101004:
            return "M416突击步枪";
        break;
        case 101003:
            return "SCAR-L突击步枪";
        break;
        case 101002:
            return "M16A4突击步枪";
        break;
        case 101009:
            return "Mk47突击步枪";
        break;
        case 101006:
            return "AUG突击步枪";
        break;
        case 101005:
            return "Groza突击步枪";
        break;
        case 101010:
            return "G36C突击步枪";
        break;
        case 101007:
            return "QBZ突击步枪";
        break;
        case 101011:
            return "AC-VAL突击步枪";
        break;
        case 101012:
            return "蜜獾突击步枪";
        break;
        //连狙
        case 103009:
            return "SLR射手步枪";
        break;
        case 103005:
            return "VSS射手步枪";
        break;
        case 103006:
            return "Mini14射手步枪";
        break;
        case 103010:
            return "QBU射手步枪";
        break;
        case 103004:
            return "SKS射手步枪";
        break;
        case 103007:
            return "MK14射手步枪";
        break;
        case 103014:
            return "MK20-H射手步枪";
        break;
        case 103013:
            return "M417射手步枪";
        break;
        //连狙
        case 103012:
            return "AMR狙击枪";
        break;
        case 103015:
            return "M200幻神";
        break;
        case 103003:
            return "AWM狙击枪";
        break;
        case 103002:
            return "M24狙击枪";
        break;
        case 103011:
            return "莫甘娜辛狙击枪";
        break;
        case 103001:
            return "Kar98K狙击枪";
        break;
        case 103008:
            return "Win94狙击枪";
        break;
        //机关枪
        case 105001:
            return "M249轻机枪";
        break;
        case 105002:
            return "DP-28轻机枪";
        break;
        case 105010:
            return "MG3轻机枪";
        break;
        case 107001:
            return "十字弩";
        break;
        case 107007:
            return "爆炸猎弓";
        break;
        //冲锋枪
        case 102001:
            return "UZI冲锋枪";
        break;
        case 102003:
            return "Vector冲锋枪";
        break;
        case 100103:
            return "PP-19冲锋枪";
        break;
        case 102007:
            return "MP5K冲锋枪";
        break;
        case 102002:
            return "UMP-45冲锋枪";
        break;
        case 102004:
            return "汤姆逊冲锋枪";
        break;
        case 102105:
            return "P90冲锋枪";
        break;
        case 102005:
            return "野牛冲锋枪";
        break;
        case 106012:
            return "双持巨轮手枪";
        break;
        case 107909:
            return "迫击炮";
        break;
        case 107005:
            return "铁拳火箭筒";
        break;
        //霰弹枪
        case 104001:
            return "S686霰弹枪";
        break;
        case 104002:
            return "S1897霰弹枪";
        break;
        case 104003:
            return "S12K霰弹枪";
        break;
        case 104004:
            return "DBS霰弹枪";
        break;
        case 104100:
            return "SPAS-12霰弹枪";
        break;
        //投掷爆炸物
        case 307104:
            return "烟雾榴弹";
        break;
        case 602004:
            return "手榴弹";
        break;
        case 602003:
            return "燃烧瓶";
        break;
        case 602002:
            return "烟雾弹";
        break;
        case 602001:
            return "震撼弹";
        break;
        //近战武器
        case 108003:
            return "镰刀";
        break;
        case 108002:
            return "撬棍";
        break;
        case 108001:
            return "大砍刀";
        break;
        case 108004:
            return "平底锅";
        break;
        case 0:
            return "空手";
        break;
        default:
            return "不知道";
        break;
    }
    return nullptr;
}

inline float 驱动_Recoil(int WeaponId)
{
	switch (WeaponId)
	{
	    case 105002:
			return 0.20;
			break;
	    case 101012:
			return 0.30;
			break;
	    case 101013:
	    case 101002:
	    case 102007:
	    case 102005:
	    case 102004:
	    case 102003:
	    case 102002:
	    case 102001:
	    case 105010:
	    case 105012:
			return 0.60;
			break;
	    case 101004:
	    case 101003:
	    case 101001:
			return 1.75;
			break;
	    case 101011:
	    case 101008:
	    case 102105:
			return 0.90;
			break;
		case 101010:
			return 1.40;
			break;
		default:
			return 0.0;
			break;
	}
	return 0.0;
}

inline float Recoil(int WeaponId)
{
	switch (WeaponId)
	{
	    case 101010:
	    case 101002:
			return 1.30;
			break;
	    case 101007:
	    case 101006:
	    case 101005:
	    case 101012:
	    case 102008:
	    case 101013:
			return 0.3;
			break;
		case 101004:
		case 101011:
		case 101003:
		case 101001:
			return 1.50;
			break;
		default:
			return 1.0;
			break;
	}
	return 1.0;
}

inline float 驱动_GetWeaponId(int WeaponId)
{
  switch (WeaponId)
  {
    case 106004:
	  return 0.80;
	  break;
	case 103007:
	  return 0.25;
	  break;
	case 105001:
	case 102008:
	  return 0.35;
	  break;
	case 101012:
	case 101001:
	  return 0.53;
	  break;
	case 101008:
	  return 0.45;
	  break;
	case 101007:
	  return 0.70;
	  break;	
	case 101004:
	case 101003:
	case 106011:
	case 106008:
	  return 0.60;
	  break;
	case 101006:
	case 101010:
	case 101005:
	  return 0.50;
	  break;
	case 102105:
	case 102004:
	case 102003:
	case 102002:
	case 102001:
	case 105010:
	case 105012:
	case 105002:
	  return 0.10;
	  break;
    default:
	  return 0.20;
	  break;
  }
  return 0.20;
}

inline float GetWeaponId(int WeaponId)
{
	switch (WeaponId)
	{
		case 102105:
		case 101009:
		case 105010:
		case 105002:
			return 0.30;
			break;
	    case 103007:
	    case 102008:
			return 0.55;
			break;
	    case 103005:
	    case 102007:
	    case 102004:
	    case 102003:
	    case 102002:
	    case 102001:
	    case 106008:
	    case 106011:
	    case 105012:
			return 0.40;
			break;
	    case 102005:
			return 0.45;
			break;
		case 101001:
		case 106004:
			return 0.95;
			break;
		case 101008:
		case 101005:
		case 101012:
			return 0.85;
			break;
		case 101006:
		case 101004:
		case 101007:
		case 101003:
		case 105001:
			return 0.65;
			break;
		case 101011:
		case 101002:
			return 0.40;
			break;
		case 101013:
			return 0.34;
			break;
	    case 101010:
			return 0.60;
			break;
		default:
			return 0.85;
			break;
	}
	return 1.0;
}

inline float getScopeAcc(int Scope){
    switch (Scope) {
        case 0:
            return 1.0f;
            break;
        case 1:
            return .98f;
            break;
        case 2:
            return .95f;
            break;
        case 3:
            return .94f;
            break;
        case 4:
            return .92f;
            break;
        case 5:
            return .9f;
            break;
        case 6:
            return .88f;
            break;
        case 7:
            return .86f;
            break;
        case 8:
            return .85f;
            break;
        default:
            return .9f;
            break;
    }
}

inline const char *GetHol(int dz)
{
    static std::map<int, const char*> holMap = {
        {655360, "自救"},
        {4112, "探头"},
        {4128, "探头"},
        {2064, "拉栓"},
        {2065, "拉栓"},
        {2067, "拉栓"},
        {2080, "拉栓"},
        {2081, "拉栓"},
        {2083, "拉栓"},
        {2113, "拉栓"},
        {2115, "拉栓"},
        {6160, "拉栓"},
        {6176, "拉栓"},
        {288, "开火"},
        {33, "行走"},
        {17, "行走"},
        {65, "行走"},
        {4194320, "坐车"},
        //{1048576, "坐船"},
        //{1050624, "坐船"},
        {273, "开火"},
        {67108880, "翻墙"},
        {320, "开火"},
        {16, "站立"},
        {4384, "开火"},
        {144, "跳跃"},
        {147, "跳跃"},
        {145, "跳跃"},
        {33554448, "跳伞"},
        {32, "蹲着"},
        {64, "趴着"},
        {6552, "掐雷"},
        {19, "奔跑"},
        {35, "奔跑"},
        {272, "开枪"},
        {1040, "开镜"},
        {1041, "开镜"},
        {1056, "开镜"},
        {1057, "开镜"},
        {1088, "开镜"},
        {5136, "开镜"},
        {5137, "开镜"},
        {5152, "开镜"},
        {5153, "开镜"},
        {2097168, "开车"},
        {67, "爬行"},
        {536870928, "跳舞"},
        {528, "换弹"},
        {544, "换弹"},
        {529, "换弹"},
        {545, "换弹"},
        {576, "换弹"},
        {131072, "倒地"},
        {524303, "倒地"},
        {524304, "倒地"},
        {524289, "倒地"},
        {32784, "挥拳"},
        {32785, "挥拳"},
        {32787, "挥拳"},
        {32800, "挥拳"},
        {32801, "挥拳"},
        {32803, "挥拳"},
        {32912, "挥拳"},
        {32913, "挥拳"},
        {32915, "挥拳"},
        {8208, "切枪"},
        {16777219, "游泳"},
        {16777216, "游泳"},
        {16777217, "游泳"},
        {524288, "倒地"},
        {65540, "打药"},
        {65541, "打药"},
        {65542, "打药"},
        {65543, "打药"},
        {65544, "打药"},
        {65545, "打药"},
        {65546, "打药"},
        {65547, "打药"},
        {65548, "打药"},
        {65549, "打药"},
        {65550, "打药"},
        {65551, "打药"},
        {65552, "打药"},
        {65553, "打药"},
        {65554, "打药"},
        {65555, "打药"},
        {65556, "打药"},
        {65557, "打药"},
        {65558, "打药"},
        {65559, "打药"},
        {65560, "打药"},
        {65561, "打药"},
        {65562, "打药"},
        {65563, "打药"},
        {65564, "打药"},
        {65565, "打药"},
        {65566, "打药"},
        {65567, "打药"},
        {65568, "打药"},
        {65569, "打药"},
        {65570, "打药"},
        {65571, "打药"},
        {65572, "打药"},
        {65573, "打药"},
        {65574, "打药"},
        {65575, "打药"},
        {65576, "打药"},
        {65577, "打药"},
        {65578, "打药"},
        {65579, "打药"},
        {65580, "打药"},
        {262161, "打药"},
        {262160, "打药"},
        {262176, "打药"},
        {262177, "打药"},
        {262208, "打药"},
        {262209, "打药"},
        {16390, "投掷"},
        {16391, "投掷"},
        {16392, "投掷"},
        {16393, "投掷"},
        {16394, "投掷"},
        {16395, "投掷"},
        {16396, "投掷"},
        {16397, "投掷"},
        {16398, "投掷"},
        {16399, "投掷"},
        {16400, "投掷"},
        {16401, "投掷"},
        {16402, "投掷"},
        {16403, "投掷"},
        {16404, "投掷"},
        {16405, "投掷"},
        {16406, "投掷"},
        {16407, "投掷"},
        {16408, "投掷"},
        {16409, "投掷"},
        {16410, "投掷"},
        {16411, "投掷"},
        {16412, "投掷"},
        {16413, "投掷"},
        {16414, "投掷"},
        {16415, "投掷"},
        {17410, "投掷"},
        {17411, "投掷"},
        {17412, "投掷"},
        {17413, "投掷"},
        {17414, "投掷"},
        {17415, "投掷"},
        {17416, "投掷"},
        {17417, "投掷"},
        {17418, "投掷"},
        {17419, "投掷"},
        {17420, "投掷"},
        {17421, "投掷"},
        {17422, "投掷"},
        {17423, "投掷"},
        {17424, "投掷"},
        {17425, "投掷"},
        {17426, "投掷"},
        {17427, "投掷"},
        {17428, "投掷"},
        {17429, "投掷"},
        {17430, "投掷"}
    };

    auto it = holMap.find(dz);
    if (it != holMap.end()) {
        return it->second;
    }
    return "0"; // 如果没有匹配的键值对，则返回默认字符串
}

inline const char* Getagrenade(int value) {
    const char* stringValue = nullptr;    
    switch(value) {
        case 602001:
            stringValue = "小心震爆弹";
            break;
        case 602003:
            stringValue = "小心燃烧瓶";
            break;
        case 602004:
            stringValue = "小心有手雷";
            break;
        case 602075:
            stringValue = "小心铝热弹";
            break;
        default:
            stringValue = nullptr;
            break;
    }

    return stringValue;
}


inline const char *VH_name(unsigned int id)
{
	static char name[30];
	memset(name, '\0', sizeof(name));
	if (id >= 3306001 && id<= 336010)//不知道什么b原因读取不到
		strcpy(name,"小轿车");
	else if (id >= 3306001 && id <= 3306010)//另写一行
		strcpy(name, "小轿车");
	else if (id >= 3301001 && id <= 3303010)
		strcpy(name, "蹦蹦");
	else if (id >= 3334001 && id <= 3334010)
		strcpy(name, "滑翔机");
	else if (id >= 3309001 && id <= 3309010)
		strcpy(name, "三轮摩托");
	else if (id >= 3325001 && id <= 3325010)
		strcpy(name, "快艇");
	else if (id >= 3313001 && id <= 3313010)
		strcpy(name, "敞篷跑车");
	else if (id >= 3336001 && id <= 3336010)
		strcpy(name, "带蓬跑车");
	else if (id >= 3307001 && id <= 3307010)
		strcpy(name,"宝宝巴士");
    else if (id >= 3350001 && id <= 3350010)
		strcpy(name,"宝马740Li");
	else if (id >= 3302001 && id <= 3302010)
		strcpy(name,"有篷吉普");
	else if (id >= 3303001 && id <= 3303010)
		strcpy(name,"无蓬吉普");
	else if (id >= 3304001 && id <= 3304010)
		strcpy(name,"吉普车");
	else if (id >= 3314001 && id <= 3314010)
		strcpy(name,"小型皮车");
	else if (id >= 3322001 && id <= 3322010)
		strcpy(name,"雪地吉普");
	else if (id >= 3324001 && id <= 3324010)
		strcpy(name,"雪地摩托");
	else if (id >= 3321001 && id <= 3321004)
		strcpy(name,"雪地摩托车");
	else if (id >= 3308001 && id <= 3308010)
		strcpy(name,"双轮摩托车");
	else if (id >= 3310001 && id <= 3310010)
		strcpy(name,"大型皮卡");
	else if (id >= 3315001 && id <= 3315010)
		strcpy(name,"小绵羊");
    else if (id >= 3351001 && id <= 3351010)
		strcpy(name,"拉力赛车");
	else if (id >= 3326001 && id <= 3326010)
		strcpy(name,"摩托艇");
	else if (id >= 3329001 && id <= 3329010)
		strcpy(name,"装甲车");
	else if (id >= 3312001 && id <= 3312010)
		strcpy(name,"有蓬跑车");
	else if (id >= 3335001 && id <= 3335010)
		strcpy(name,"大脚车");
	else if (id >= 3323001 && id <= 3323010)
		strcpy(name,"三轮车");
	else if (id >= 3348001 && id <= 3348010)
		strcpy(name,"姥爷车");
	else if (id >= 3349001 && id <= 3349010)
		strcpy(name,"敞篷跑车");
	else if (id >= 3342001 && id <= 3342010)
		strcpy(name,"越野摩托车");
	else if (id >= 3311002 && id <= 3311010)
		strcpy(name,"皮卡车");
	else if (id >= 3309002 && id <= 3309010)
		strcpy(name,"三轮摩托车");
	else if (id >= 3348001 && id <= 3348010)
		strcpy(name,"旅行车");
    else if (id >= 3347001 && id <= 3347001)
		strcpy(name,"自行车");
    else if (id >= 3355001 && id <= 3355001)
		strcpy(name,"小森口");
    else if (id >= 3354001 && id <= 3354001)
		strcpy(name,"森口");
    else if (id >= 3356001 && id <= 3356001)
		strcpy(name,"渣土车");
    else if (id >= 3357001 && id <= 3357001)
		strcpy(name,"挖掘机");
    else if (id >= 3353001 && id <= 3353001)
		strcpy(name,"沙漠越野车");
    else 
        strcpy(name,"Error");
	return name;
}