#ifndef 辅助类_H
#define 辅助类_H

#include <iostream>
#include <pthread.h>
#include <dirent.h>
#include <regex.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fstream>
#include <vector>
#include <map>
#include <cstdio>
#include <stdio.h>
#include <sys/syscall.h>
#include <sys/socket.h>
#include <linux/netlink.h>

#include <ctype.h>
#include <dirent.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/fcntl.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <time.h>
#include <unistd.h>


#include "./CPUaffinity/timer.h"
#include "obfuscate.h"

#define PI 3.141592653589793238

class c_driver {
private:
  int has_upper = 0;
  int has_lower = 0;
  int has_symbol = 0;
  int has_digit = 0;
  int fd;
  pid_t pid;

  typedef struct _COPY_MEMORY {
    pid_t pid;
    uintptr_t addr;
    void *buffer;
    size_t size;
  } COPY_MEMORY, *PCOPY_MEMORY;

  typedef struct _MODULE_BASE {
    pid_t pid;
    char *name;
    uintptr_t base;
  } MODULE_BASE, *PMODULE_BASE;


  enum OPERATIONS {
    OP_INIT_KEY = 0x800,
    OP_READ_MEM = 0x801,
    OP_WRITE_MEM = 0x802,
    OP_MODULE_BASE = 0x803
  };

  int symbol_file(const char *filename) {
    // 判断文件名是否含小写并且不含大写不含数字不含符号
    int length = strlen(filename);
    for (int i = 0; i < length; i++) {
      if (islower(filename[i])) {
        has_lower = 1;
      } else if (isupper(filename[i])) {
        has_upper = 1;
      } else if (ispunct(filename[i])) {
        has_symbol = 1;
      } else if (isdigit(filename[i])) {
        has_digit = 1;
      }
    }
    return has_lower && !has_upper && !has_symbol && !has_digit;
  }
  
  
  char *driver_path() {
    struct dirent *de;
    DIR *dr = opendir("/proc");
    char *device_path = NULL;

    if (dr == NULL) {
      printf("Could not open /proc directory");
      return NULL;
    }



    while ((de = readdir(dr)) != NULL) {
      if (strlen(de->d_name) != 6 || strcmp(de->d_name, "NVTSPI") == 0 || strcmp(de->d_name, "ccci_log") == 0 || strcmp(de->d_name, "aputag") == 0 || strcmp(de->d_name, "asound") == 0 || strcmp(de->d_name, "clkdbg") == 0 || strcmp(de->d_name, "crypto") == 0 || strcmp(de->d_name, "modules") == 0 || strcmp(de->d_name, "mounts") == 0 || strcmp(de->d_name, "pidmap") == 0 || strcmp(de->d_name, "phoenix") == 0 || strcmp(de->d_name, "uptime") == 0 || strcmp(de->d_name, "vmstat") == 0) {
        continue;
      }
      int is_valid = 1;
      for (int i = 0; i < 6; i++) {
        if (!isalnum(de->d_name[i])) {
          is_valid = 0;
          break;
        }
      }
        if (is_valid) {
            device_path = (char*)malloc(11 + strlen(de->d_name));
            sprintf(device_path, "/proc/%s", de->d_name);
            struct stat sb;
            if (stat(device_path, &sb) == 0 && S_ISREG(sb.st_mode)) {
                break;
            } else {
                free(device_path);
                device_path = NULL;
            }
        }
    }
    puts(device_path);
    closedir(dr);
    return device_path;
  }
  
   char *find_driver_path() {
    // 打开目录
		const char *dev_path = "/dev";
		DIR *dir = opendir(dev_path);
		if (dir == NULL){
			printf("无法打开/dev目录\n");
			return NULL;
		}

		char *files[] = { "wanbai", "CheckMe", "Ckanri", "lanran","video188"};
		struct dirent *entry;
		char *file_path = NULL;
		while ((entry = readdir(dir)) != NULL) {
			// 跳过当前目录和上级目录
			if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
				continue;
			}

			size_t path_length = strlen(dev_path) + strlen(entry->d_name) + 2;
			file_path = (char *)malloc(path_length);
			snprintf(file_path, path_length, "%s/%s", dev_path, entry->d_name);
			for (int i = 0; i < 5; i++) {
				if (strcmp(entry->d_name, files[i]) == 0) {
					printf("驱动文件：%s\n", file_path);
					closedir(dir);
					return file_path;
				}
			}

			// 获取文件stat结构
			struct stat file_info;
			if (stat(file_path, &file_info) < 0) {
				free(file_path);
				file_path = NULL;
				continue;
			}

			// 跳过gpio接口
			if (strstr(entry->d_name, "gpiochip") != NULL) {
				free(file_path);
				file_path = NULL;
				continue;
			}

			// 检查是否为驱动文件
			if ((S_ISCHR(file_info.st_mode) || S_ISBLK(file_info.st_mode))
				&& strchr(entry->d_name, '_') == NULL && strchr(entry->d_name, '-') == NULL && strchr(entry->d_name, ':') == NULL) {
				// 过滤标准输入输出
				if (strcmp(entry->d_name, "stdin") == 0 || strcmp(entry->d_name, "stdout") == 0
					|| strcmp(entry->d_name, "stderr") == 0) {
					free(file_path);
					file_path = NULL;
					continue;
				}
				
				size_t file_name_length = strlen(entry->d_name);
				time_t current_time;
				time(&current_time);
				int current_year = localtime(&current_time)->tm_year + 1900;
				int file_year = localtime(&file_info.st_ctime)->tm_year + 1900;
				//跳过1980年前的文件
				if (file_year <= 1980) {
					free(file_path);
					file_path = NULL;
					continue;
				}
				
				time_t atime = file_info.st_atime;
				time_t ctime = file_info.st_ctime;
				// 检查最近访问时间和修改时间是否一致并且文件名是否是symbol文件
				if ((atime == ctime)/* && symbol_file(entry->d_name)*/) {
					//检查mode权限类型是否为S_IFREG(普通文件)和大小还有gid和uid是否为0(root)并且文件名称长度在7位或7位以下
					if ((file_info.st_mode & S_IFMT) == 8192 && file_info.st_size == 0
						&& file_info.st_gid == 0 && file_info.st_uid == 0 && file_name_length <= 9) {
						printf("驱动文件：%s\n", file_path);
						closedir(dir);
						return file_path;
					}
				}
			}
			free(file_path);
			file_path = NULL;
		}
		closedir(dir);
		return NULL;
	}
 
  
  
  
public:
  // 构造函数：初始化驱动
  c_driver() {
 int isll=1;
    printf("选择你刷入的驱动(输入1是dev驱动 | 输入2是proc驱动)\n");
    scanf("%d", &isll); 
    char *device_name;
    if(isll==1)
  device_name= find_driver_path();
  else
  device_name= driver_path();
    if (!device_name) {
      fprintf(stderr, "未找到驱动文件\n");
      exit(EXIT_FAILURE);
    }

    fd = open(device_name, O_RDWR);
    free(device_name);

    if (fd == -1) {
      perror("[-] 链接驱动失败");
     exit(EXIT_FAILURE);
    }
  }

	~c_driver() {
		//wont be called
		if (fd > 0)
			close(fd);
	}

	void initialize(pid_t pid) { this->pid = pid; }

  bool init_key(char *key) {
    char buf[0x100];
    strcpy(buf, key);
    if (ioctl(fd, OP_INIT_KEY, buf) != 0) {
      return false;
    }
    return true;
  }

  bool read(uintptr_t addr, void *buffer, size_t size) {
    COPY_MEMORY cm;

    cm.pid = this->pid;
    cm.addr = addr;
    cm.buffer = buffer;
    cm.size = size;

    if (ioctl(fd, OP_READ_MEM, &cm) != 0) {
      return false;
    }
    return true;
  }

  bool write(uintptr_t addr, void *buffer, size_t size) {
    COPY_MEMORY cm;

    cm.pid = this->pid;
    cm.addr = addr;
    cm.buffer = buffer;
    cm.size = size;

    if (ioctl(fd, OP_WRITE_MEM, &cm) != 0) {
      return false;
    }
    return true;
  }

  template <typename T> T read(uintptr_t addr) {
    T res;
    if (this->read(addr, &res, sizeof(T)))
      return res;
    return {};
  }

  template <typename T> bool write(uintptr_t addr, T value) {
    return this->write(addr, &value, sizeof(T));
  }

  uintptr_t get_module_base(char *name) {
    MODULE_BASE mb;
    char buf[0x100];
    strcpy(buf, name);
    mb.pid = this->pid;
    mb.name = buf;

    if (ioctl(fd, OP_MODULE_BASE, &mb) != 0) {
      return 0;
    }
    return mb.base;
  }

};

class Kernel {
private:
    int has_upper = 0;
	int has_lower = 0;
	int has_symbol = 0;
	int has_digit = 0;
    int fd;
    pid_t pid;
    typedef struct _COPY_MEMORY {
        pid_t pid;
        uintptr_t addr;
        void* buffer;
        size_t size;
    } COPY_MEMORY, *PCOPY_MEMORY;

    typedef struct _MODULE_BASE {
        pid_t pid;
        char* name;
        uintptr_t base;
    } MODULE_BASE, *PMODULE_BASE;

    enum OPERATIONS {
        OP_INIT_KEY = 0x800,
        OP_READ_MEM = 0x801,
        OP_WRITE_MEM = 0x802,
        OP_MODULE_BASE = 0x803,
        OP_HIDE_PROCESS = 0x804,
    };
public:
    int get_dev();
    Kernel();
    ~Kernel();
    void 初始化读写(int pid);
    bool readv(uintptr_t addr, void *buffer, size_t size);
    bool writev(uintptr_t addr, void *buffer, size_t size);
    template<typename T>
    T Read(uintptr_t address);
    uintptr_t get_module_base(char* name);
    uintptr_t getPtr64(uintptr_t addr);
    uintptr_t getPtr32(uintptr_t addr);
    float getFloat(uintptr_t addr);
    int getDword(uintptr_t addr);
    int WriteDword(long int addr, int value);
    float WriteFloat(long int addr, float value);
    void writefloat(unsigned long addr,float data);
    int getPID(const char* packageName);
    void getUTF8(char * buf, unsigned long namepy);
    bool init_key(char* key);
    char *driver_path();
    int symbol_file(const char *filename);
};

#include "Draw.h"
#include "imgui_impl_opengl3.h"
#include "结构体.h"
#include "骨骼.hpp"



class StringFloatMap {
private:
    map<string, vector<float>> data;

public:
    void add(string key, float value1, float value2) {
        vector<float> values;
        values.push_back(value1);
        values.push_back(value2);
        data[key] = values;
    }

    void remove(string key) {
        data.erase(key);
    }
    
    bool exists(string key) {
        return data.find(key) != data.end();
    }
    
    string calculateKey(float 坐标X, float 坐标Y) {
        int maps = 999;
        string 键名;
        for (const auto& pair : data) {
            const vector<float>& values = pair.second;
            int result = (int)sqrt(pow(坐标X - values[0], 2) + pow(坐标Y - values[1], 2))*0.01;
            //printf("测试值:%f \n",result);
            if (result < maps) {
                //return pair.first;
                maps = result;
                键名 = pair.first;
                //break;
            }
        }
        if (maps < 20) {
          return 键名;
        }
        return "";  // 如果没有满足条件的键名，则返回空字符串
    }
};


class Timer {
private:
    std::map<std::string, int> timers;

public:
    void addTimer(const std::string& name, int seconds) {
        if (timers.find(name) == timers.end()) {
            timers[name] = seconds;
        }
    }

    void updateTimers() {
        for (auto& timer : timers) {
            timer.second++;
        }
    }

    void checkAndRemoveTimers() {
        for (auto it = timers.begin(); it != timers.end();) {
            if (it->second == 1500) {
                it = timers.erase(it);
            } else {
                ++it;
            }
        }
    }

    int getTimerSeconds(const std::string& name) {
        auto it = timers.find(name);
        if (it != timers.end()) {
            return it->second;
        }
        return -1;
    }

    void removeTimer(const std::string& name) {
        timers.erase(name);
    }

    bool hasTimer(const std::string& name) { //判断是否存在
        return timers.find(name) != timers.end();
    }

    void renameTimer(const std::string& oldName, const std::string& newName) {
        if (timers.find(oldName) != timers.end()) {
            timers[newName] = timers[oldName];
            timers.erase(oldName);
        }
    }
};


class 计算
{
public:
    int 计算距离(D3DVector 自身坐标, D3DVector 对方坐标);
    D4DVector 计算屏幕坐标(float 矩阵[16], D3DVector 人物坐标, float px, float py);
    D2DVector 计算屏幕坐标2(float 矩阵[16], D3DVector 人物坐标, float px, float py);
    float 计算屏幕距离(D2DVector &坐标, float px, float py);
    骨骼数据 计算骨骼(float (&矩阵)[16], D3DVector (&骨骼)[17], float px, float py);
    D2DVector rotateCoord(float angle, float objRadar_x, float objRadar_y);
    D2DVector rotateCoord(D3DVector Enemy, D3DVector RealPerson);
};
// 绘图
class 绘图
{
    struct 颜色
    {
        ImColor 红色 = ImColor(255, 0, 0, 255);
        ImColor 白色 = ImColor(255, 255, 255, 255);
        ImColor 蓝色 = ImColor(0, 0, 255, 255);
        ImColor 绿色 = ImColor(0, 255, 0, 255);
        ImColor 黄色 = ImColor(255, 255, 0, 255);
        ImColor 黑色 = ImColor(0, 0, 0, 255);
    };

public:
    struct VecTor2
    {
        float x;
        float y;
        VecTor2()
        {
            this->x = 0;
            this->y = 0;
        }
        VecTor2(float x, float y)
        {
            this->x = x;
            this->y = y;
        }
        bool operator!=(const VecTor2 &Pos)
        {
            if (this->x != Pos.x || this->y != Pos.y)
            {
                return true;
            }
            return false;
        }
        VecTor2 operator+(float v) const
        {
            return VecTor2(x + v, y + v);
        }
        VecTor2 operator-(float v) const
        {
            return VecTor2(x - v, y - v);
        }
        VecTor2 operator*(float v) const
        {
            return VecTor2(x * v, y * v);
        }
        VecTor2 operator/(float v) const
        {
            return VecTor2(x / v, y / v);
        }
        VecTor2 &operator+=(float v)
        {
            x += v;
            y += v;
            return *this;
        }
        VecTor2 &operator-=(float v)
        {
            x -= v;
            y -= v;
            return *this;
        }
        VecTor2 &operator*=(float v)
        {
            x *= v;
            y *= v;
            return *this;
        }
        VecTor2 &operator/=(float v)
        {
            x /= v;
            y /= v;
            return *this;
        }
        VecTor2 operator+(const VecTor2 &v) const
        {
            return VecTor2(x + v.x, y + v.y);
        }
        VecTor2 operator-(const VecTor2 &v) const
        {
            return VecTor2(x - v.x, y - v.y);
        }
        VecTor2 operator*(const VecTor2 &v) const
        {
            return VecTor2(x * v.x, y * v.y);
        }
        VecTor2 operator/(const VecTor2 &v) const
        {
            return VecTor2(x / v.x, y / v.y);
        }
        VecTor2 &operator+=(const VecTor2 &v)
        {
            x += v.x;
            y += v.y;
            return *this;
        }
        VecTor2 &operator-=(const VecTor2 &v)
        {
            x -= v.x;
            y -= v.y;
            return *this;
        }
        VecTor2 &operator*=(const VecTor2 &v)
        {
            x *= v.x;
            y *= v.y;
            return *this;
        }
        VecTor2 &operator/=(const VecTor2 &v)
        {
            x /= v.x;
            y /= v.y;
            return *this;
        }
    };
    struct VecTor3
    {
        float x;
        float y;
        float z;
        VecTor3()
        {
            this->x = 0;
            this->y = 0;
            this->z = 0;
        }
        VecTor3(float x, float y, float z)
        {
            this->x = x;
            this->y = y;
            this->z = z;
        }
        bool operator!=(const VecTor3 &Pos)
        {
            if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z)
            {
                return true;
            }
            return false;
        }
        VecTor3 operator+(float v) const
        {
            return VecTor3(x + v, y + v, z + v);
        }
        VecTor3 operator-(float v) const
        {
            return VecTor3(x - v, y - v, z - v);
        }
        VecTor3 operator*(float v) const
        {
            return VecTor3(x * v, y * v, z * v);
        }
        VecTor3 operator/(float v) const
        {
            return VecTor3(x / v, y / v, z / v);
        }
        VecTor3 &operator+=(float v)
        {
            x += v;
            y += v;
            z += v;
            return *this;
        }
        VecTor3 &operator-=(float v)
        {
            x -= v;
            y -= v;
            z -= v;
            return *this;
        }
        VecTor3 &operator*=(float v)
        {
            x *= v;
            y *= v;
            z *= v;
            return *this;
        }
        VecTor3 &operator/=(float v)
        {
            x /= v;
            y /= v;
            z /= v;
            return *this;
        }
        VecTor3 operator+(const VecTor3 &v) const
        {
            return VecTor3(x + v.x, y + v.y, z + v.z);
        }
        VecTor3 operator-(const VecTor3 &v) const
        {
            return VecTor3(x - v.x, y - v.y, z - v.z);
        }
        VecTor3 operator*(const VecTor3 &v) const
        {
            return VecTor3(x * v.x, y * v.y, z * v.z);
        }
        VecTor3 operator/(const VecTor3 &v) const
        {
            return VecTor3(x / v.x, y / v.y, z / v.z);
        }
        VecTor3 &operator+=(const VecTor3 &v)
        {
            x += v.x;
            y += v.y;
            z += v.z;
            return *this;
        }
        VecTor3 &operator-=(const VecTor3 &v)
        {
            x -= v.x;
            y -= v.y;
            z -= v.z;
            return *this;
        }
        VecTor3 &operator*=(const VecTor3 &v)
        {
            x *= v.x;
            y *= v.y;
            z *= v.z;
            return *this;
        }
        VecTor3 &operator/=(const VecTor3 &v)
        {
            x /= v.x;
            y /= v.y;
            z /= v.z;
            return *this;
        }
    };


    struct VecTor4
    {
        float x;
        float y;
        float z;
        float w;
        VecTor4()
        {
            this->x = 0;
            this->y = 0;
            this->z = 0;
            this->w = 0;
        }
        VecTor4(float x, float y, float z, float w)
        {
            this->x = x;
            this->y = y;
            this->z = z;
            this->w = w;
        }
        bool operator!=(const VecTor4 &Pos)
        {
            if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z || this->w != Pos.w)
            {
                return true;
            }
            return false;
        }
        VecTor4 operator+(float v) const
        {
            return VecTor4(x + v, y + v, z + v, w + v);
        }
        VecTor4 operator-(float v) const
        {
            return VecTor4(x - v, y - v, z - v, w - v);
        }
        VecTor4 operator*(float v) const
        {
            return VecTor4(x * v, y * v, z * v, w * v);
        }
        VecTor4 operator/(float v) const
        {
            return VecTor4(x / v, y / v, z / v, w / v);
        }
        VecTor4 &operator+=(float v)
        {
            x += v;
            y += v;
            z += v;
            w += v;
            return *this;
        }
        VecTor4 &operator-=(float v)
        {
            x -= v;
            y -= v;
            z -= v;
            w -= v;
            return *this;
        }
        VecTor4 &operator*=(float v)
        {
            x *= v;
            y *= v;
            z *= v;
            w *= v;
            return *this;
        }
        VecTor4 &operator/=(float v)
        {
            x /= v;
            y /= v;
            z /= v;
            w /= v;
            return *this;
        }
        VecTor4 operator+(const VecTor4 &v) const
        {
            return VecTor4(x + v.x, y + v.y, z + v.z, w + v.w);
        }
        VecTor4 operator-(const VecTor4 &v) const
        {
            return VecTor4(x - v.x, y - v.y, z - v.z, w - v.w);
        }
        VecTor4 operator*(const VecTor4 &v) const
        {
            return VecTor4(x * v.x, y * v.y, z * v.z, w * v.w);
        }
        VecTor4 operator/(const VecTor4 &v) const
        {
            return VecTor4(x / v.x, y / v.y, z / v.z, w / v.w);
        }
        VecTor4 &operator+=(const VecTor4 &v)
        {
            x += v.x;
            y += v.y;
            z += v.z;
            w += v.w;
            return *this;
        }
        VecTor4 &operator-=(const VecTor4 &v)
        {
            x -= v.x;
            y -= v.y;
            z -= v.z;
            w -= v.w;
            return *this;
        }
        VecTor4 &operator*=(const VecTor4 &v)
        {
            x *= v.x;
            y *= v.y;
            z *= v.z;
            w *= v.w;
            return *this;
        }
        VecTor4 &operator/=(const VecTor4 &v)
        {
            x /= v.x;
            y /= v.y;
            z /= v.z;
            w /= v.w;
            return *this;
        }
    };

    颜色 颜色; // 颜色的类
    float PX, PY;
    float MIDDLE, BOTTOM, TOP;
    float left, right, top, top1, bottom;
    bool isAiming;
    void 初始化绘图(int X, int Y);
    void 初始化坐标(D4DVector &屏幕坐标, 骨骼数据 &骨骼);
    void 绘制方框(bool isboot);
    void 绘制人数(int 人机, int 真人);
    void 绘制距离(int 距离, int 队伍);
    void 绘制射线(骨骼数据 &骨骼);
    void 绘制血量(float 最大血量, float 当前血量, bool isbot);
    void 绘制名字(string 名字, bool isboot, float 计时, bool 是否掐雷, char *类名, int 阵营, int Bonecount);
    void 绘制骨骼(骨骼数据 &骨骼, D4DVector &屏幕坐标, bool LineOfSightTo, float 距离);
    void 绘制手持(int 手持, int 状态, int 子弹, int 最大子弹);
    void 绘制车辆(D4DVector 屏幕坐标, int 距离, int CarrierID);
    void 绘制自瞄触摸范围(float 触摸范围, float 触摸范围X, float 触摸范围Y);
    void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char *str);
    void 绘制字体描边(float size, int x, int y, ImVec4 color, const char *str);
    void RenderRadarScan(ImDrawList* draw_list, ImVec2 center, float width, float height, float rotationAngle, float lineLength);
    void 绘制瞄准信息();

    bool WorldTurnScreen(VecTor2 &Screen, VecTor3 World, float Matrix[]);

    void ExplosionRange(D3DVector Obj, ImColor color, float Range, float thickn, float Matrix[]);

    void Parabola(VecTor3 obj, float Matrix[]);
};
// 绘制
class 绘制
{

    struct ColorTable
    {
        float 方框颜色[4] = {1.0, 1.0, 1.0, 1.0};
        float 射线颜色[4] = {1.0, 1.0, 1.0, 1.0};
        float 骨骼颜色[4] = {1.0, 1.0, 1.0, 1.0};
        float 血量颜色[4] = {1.0, 0.0, 0.0, 1.0};
        float 距离颜色[4] = {1.0, 1.0, 1.0, 1.0};
        float 名称颜色[4] = {1.0, 1.0, 1.0, 1.0};
    };
    struct map_node
    {
        long int start_addr;   // 起始地址
        long int end_addr;     // 结束地址
        struct map_node *next; // 下一个节点
    };

    struct 压枪
    {
        float m416;
        float scar_l;
        float aug;
        float famas;
        float g36c;
        float m249;
        float akm;
        float m762;
        float 蜜獾;
        float pkm;
        float mg3;
        float mg_36;
        float p90;
        float uzi;
        float ump45;
        float vector;
        float 汤姆逊;
        float 野牛;
        float mini14;
        float sks;
        float m417;
        float mk20_h;
        float mk12;
        float vss;
        float 扫车 = 1.2f;
    };

public:
    float mk20, m417 = 1.0f;
    float 轻型压枪力度 = 1.f;
    float 拇指压枪力度 = 0.2f;
    float 垂直压枪力度 = 0.2f;
    float 直角压枪力度 = 0.2f;
    int 世界数量;
    float 骨骼距离限制 = 300;
    int 漏打模式, 防录屏, 自瞄模式, 无后台开关, 控制延迟;
    char 卡密[250];
    bool 漏打开关;
    bool Winorlose = false;
    ImVec2 Pos;
    int winWidth = 684;
    int winHeith = 896;

    bool 是否开启自瞄页面 = false;

    压枪 压枪力;
    压枪 预判度;
    float 握把[100];

    uintptr_t 解密数组;
    long int 解密模式 = 0x4000;

    int 被瞄准对象数量 = 0;

    FILE *numSave = nullptr;

    char 悬浮窗标题[200];

    ColorTable Colorset[2]; // 颜色配置
    int pid;
    bool 线程 = 0; // 用于判断更新数据显示是否加载
    float PX, PY;  // 绘制用的分辨率
    float 真实PX, 真实PY;
    bool Validate;
    Kernel 读写; // 创建读写结构体

    float 物资颜色[4] = {0.0, 1.0, 0.0, 1.0};
    float 车辆颜色[4] = {0.0, 1.0, 0.0, 1.0};
    float 手持颜色[4] = {1.0, 1.0, 1.0, 1.0};

    StringFloatMap 手雷类;
    Timer 计时器; // 计时器

    bool Shelter[14];

    地址 地址;
    开关 按钮;
    计算 计算;
    骨骼 *骨骼;
    绘图 绘图;
    自瞄 自瞄;
    备份 备份;
    std::mutex mtx;
    自瞄信息 自瞄函数[100];
    瞄准信息 被瞄信息[100];
    自身数据 自身数据; // 创建自身数据结构体
    对象地址 对象地址; // 创建对象地址结构体
    对象信息 对象信息; // 创建敌人信息结构体数组
    void 初始化绘制(string 包名, int 真实X, int 真实Y);
    void 自瞄主线程();
    void 驱动自瞄主线程();
    int findminat();
    void 更新地址数据();
    void 多线程更新地址();
    void 更新对象地址();
    void 更新对象数据();
    void 绘制载具信息();
    void 运行绘制();
    ImColor floatArrToImColor(float arr[4]);
    void hide_process();
    string getBoxName(int id);
    void OffScreen(ImDrawList *ImDraw, D4DVector Obj, float camear, ImU32 color, float Radius, float 距离);
    void GetTouch();
    void 保存配置();
    void 读取配置();
    bool 自瞄触发(float 距离);
    const char *getMaterialName(char *name);
    int Cloudcheck();
    const char *Level(char *name);
    int Acquisitionsite();
    D3DVector Missedtyping();
    void 掩体线程();
    void InitShoot();

    void InitMaps();
    void print_maps(struct map_node *head);
    void free_maps(struct map_node *head);
    void readmaps_v(struct map_node **head);
};

class 布局
{
public:
    // 布局UI
    void 开启悬浮窗();
    void 绘制悬浮窗();
    int 初始化程序();
};

#endif
