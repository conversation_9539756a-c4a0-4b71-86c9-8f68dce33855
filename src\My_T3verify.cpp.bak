#include <string>
#include <time.h>           //读时间获取
#include <dirent.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/system_properties.h>

#include "My_T3verify.h"

//#include "cJSON.h"
//#include "Encrypt.h"
#include "Mysshttp.h"


char 到期时间[100] = ""; // 在这里定义

char *getAndroid_boot_serialno() {
    char *prop_value = (char *) calloc(256, sizeof(char));
    ::__system_property_get((char *)("ro.boot.serialno"), prop_value);
    return prop_value;
}
char* getMacAddresses(const char *str_name) {
    char *macAddress;
    struct dirent *entry;
    DIR *dir = opendir("/sys/class/net");
    if (dir == NULL) {
        perror("opendir");
        return (char *)"";
    }
    while ((entry = readdir(dir)) != NULL) {
        if (strncmp(entry->d_name, str_name, strlen(str_name)) == 0) {
            char macFile[128] = {0};
            snprintf(macFile, sizeof(macFile), "/sys/class/net/%s/address", entry->d_name);
            FILE *file = fopen(macFile, "r");
            if (file) {
                char local_str[64] = {0};
                macAddress = (char *)calloc(64, sizeof(char));
                fgets(local_str, 64, file);
                fclose(file);
                snprintf(macAddress, strlen(local_str), "%s", local_str);
            }
        }
    }
    closedir(dir);
    return macAddress;
}
bool loadConfig_ver(char *cfgPath, void *data, size_t size) {
    int fd = open(cfgPath, O_RDONLY);
   	if (fd > 0) {
   		read(fd, data, size);
   		close(fd);
    	return true;
    } else {
   	    return false;
    }
}
bool saveConfig_ver(char *cfgPath, void *data, size_t size) {
    int fd = open(cfgPath, O_TRUNC | O_WRONLY | O_CREAT, S_IRUSR | S_IWUSR);
    if (fd > 0) {
    	write(fd, data, size);	// 写入文本 // close(fd); 
    	close(fd);
   		return true;
   	} else {
 	    return false;
    }
}     
char *my_shell(const char *command) {
	FILE *pipe = NULL;
	char line[256] = { };
    char *result = (char *) calloc(2048, sizeof(char));
	memset(result, 0, 2048);
	pipe = popen(command, "r");
    if (!pipe) {
        return {};
    }
    while (!feof(pipe)) {
        if (fgets(line, sizeof(line), pipe) != NULL) {
    		strncat(result, line, strlen(line));
        } else {
        
        }
    }
	pclose(pipe);
	return result;
}

void get_middle(char *text, char *str_start, char *str_end, char *ret, size_t ret_size) {	
    memset((void *)ret, 0, ret_size);
    char *ptr_start = strstr(text, str_start);
    if (ptr_start == NULL) {
        return;
    }
    ptr_start += strlen(str_start);    
    char *ptr_end = strstr(ptr_start, str_end);
    if (ptr_end == NULL) {
        return;
    }
    int langth = ptr_end - ptr_start;
    //printf("langth : %d ... %d\n", langth, strlen(ptr_start));
    if (ret_size >= langth) {
        strncpy(ret, ptr_start, langth);
    } else {
        strncpy(ret, ptr_start, ret_size);
    }
}

char* getHEX(const char *string) {
    char chs;
    char *ret = NULL;
    char *str = NULL;
    if (!string || (ret = str = (char *)malloc(strlen(string) * 2 + 1)) == NULL) {
        return NULL;
    }
    while (*string) {
        chs = (*string & 0XF0) >> 4;
        if (chs > 9) {
            *str = chs - 10 + 'A'; //chs - 10 + 'A'
        } else {
            *str = chs + '0';
        }
        str++;
        chs = *string & 0X0F;
        if (chs > 9) {
            *str = chs - 10 + 'A'; //chs - 10 + 'A'
        } else {
            *str = chs + '0';
        }
        str++;
        string++;
    }
    *str = '\0';
    return ret;
}

char* 加base64(const char* data, char BQAQSQEQ[]) { 
    int data_len = strlen(data); 
    int prepare = 0; 
    int ret_len; 
    int temp = 0; 
    char *ret = NULL; 
    char *f = NULL; 
    int tmp = 0; 
    char changed[4]; 
    int i = 0; 
    ret_len = data_len / 3; 
    temp = data_len % 3; 
    if (temp > 0) { 
        ret_len += 1; 
    } 
    ret_len = ret_len * 4 + 1; 
    ret = (char *)malloc(ret_len);       
    if ( ret == NULL) { 
        printf("No enough memory.\n"); 
        exit(0); 
    } 
    memset(ret, 0, ret_len); 
    f = ret; 
    while (tmp < data_len) { 
        temp = 0; 
        prepare = 0; 
        memset(changed, '\0', sizeof(changed)); 
        while (temp < 3) { 
            //printf("tmp = %d\n", tmp); 
            if (tmp >= data_len) { 
                break; 
            } 
            prepare = ((prepare << 8) | (data[tmp] & 0xFF)); 
            tmp++; 
            temp++; 
        } 
        prepare = (prepare << ((3-temp) * 8)); 
        //printf("before for : temp = %d, prepare = %d\n", temp, prepare); 
        for (i = 0; i < 4 ;i++ ) { 
            if (temp < i) { 
                changed[i] = 0x40; 
            }  else { 
                changed[i] = (prepare>>((3-i)*6)) & 0x3F; 
            } 
            *f = BQAQSQEQ[changed[i]]; 
            //printf("%.2X", changed[i]); 
            f++; 
        } 
    } 
    
    *f = '\0'; 
    const char *a[3] = {"", "==", "="};
    sprintf(ret, "%s%s", ret, a[strlen(data)%3]);
    return ret;       
} 

char *解base64(const char *data, char BQAQSQEQ[]) { 
    int data_len = strlen(data); 
    int ret_len = (data_len / 4) * 3; 
    int equal_count = 0; 
    char *ret = NULL; 
    char *f = NULL; 
    int tmp = 0; 
    int temp = 0; 
    char need[4] = {0}; 
    int prepare = 0; 
    if (*(data + data_len - 1) == '=') { 
        equal_count += 1; 
    } 
    if (*(data + data_len - 2) == '=') { 
        equal_count += 1; 
    } 
    if (*(data + data_len - 3) == '=') {//seems impossible 
        equal_count += 1; 
    } 
    switch (equal_count)  { 
    case 0: 
        ret_len += 4;//3 + 1 [1 for NULL] 
        break; 
    case 1: 
        ret_len += 4;//Ceil((6*3)/8)+1 
        break; 
    case 2: 
        ret_len += 3;//Ceil((6*2)/8)+1 
        break; 
    case 3: 
        ret_len += 2;//Ceil((6*1)/8)+1 
        break; 
    } 

    ret = (char *)malloc(ret_len); 
    if (ret == NULL) { 
        printf("No enough memory.\n"); 
        exit(0); 
    } 
    memset(ret, 0, ret_len); 
    f = ret; 
    while (tmp < (data_len - equal_count)) { 
        temp = 0; 
        prepare = 0; 
        memset(need, 0, 4); 
        while (temp < 4) { 
            if (tmp >= (data_len - equal_count)) { 
                break; 
            } 
            
            char *local_ptr = (char *)strrchr(BQAQSQEQ, data[tmp]);//the last position (the only) in base[] 
            char local_ssb = (local_ptr - BQAQSQEQ); 
            prepare = (prepare << 6) | local_ssb; 
            temp++; 
            tmp++; 
        } 
        prepare = prepare << ((4-temp) * 6); 
        for (int i = 0; i < 3; i++) { 
            if (i == temp) { 
                break; 
            } 
            *f = (char)((prepare >> ((2-i) * 8)) & 0xFF); 
            f++; 
        } 
    } 
    *f = '\0'; 
    return ret; 
}



void My_获取时间戳( char sj[13]) {
        time_t t = time(NULL);
        struct tm *mtime = localtime(&t);
        //char sj[13];  
        char *sjzz = sj;
        My_itoa(mtime->tm_year + 1900, sjzz, 10);
        sjzz += 4;
        if (mtime->tm_mon + 1 < 10) { //月
            *sjzz = '0';
            sjzz++;
            My_itoa(mtime->tm_mon + 1, sjzz, 10);
            sjzz++;
        } else {
            My_itoa(mtime->tm_mon + 1, sjzz, 10);
            sjzz += 2;
        }
        if (mtime->tm_mday < 10) { //日
            *sjzz = '0';
            sjzz++;
            My_itoa(mtime->tm_mday, sjzz, 10);
            sjzz++;
        } else {
            My_itoa(mtime->tm_mday, sjzz, 10);
            sjzz += 2;
        }
        if (mtime->tm_hour < 10) { //小时
            *sjzz = '0';
            sjzz++;
            My_itoa(mtime->tm_hour, sjzz, 10);
            sjzz++;
        } else {
            My_itoa(mtime->tm_hour, sjzz, 10);
            sjzz += 2;
        }
        if (mtime->tm_min < 10) { //分钟
            *sjzz = '0';
            sjzz++;
            My_itoa(mtime->tm_min, sjzz, 10);
            sjzz++;
        }  else {
            My_itoa(mtime->tm_min, sjzz, 10);
            sjzz += 2;
        }
}
void My_itoa(int num, char *str, int radix) {
    char index[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    unsigned unum;
    int i = 0, j, k;                
    if (radix == 10 && num < 0) {
        unum = (unsigned)-num;
        str[i++] = '-';
    } else {
        unum = (unsigned)num; 
    }        
    do {
        str[i++] = index[unum % (unsigned)radix];
        unum /= radix;
    } while (unum);

    str[i] = '\0';

    //将顺序调整过来
    if (str[0] == '-')
        k = 1;
    else
        k = 0;

    char temp;
    for (j = k; j <= (i - 1) / 2; j++) {
        temp = str[j];
        str[j] = str[i - 1 + k - j];
        str[i - 1 + k - j] = temp;
    }
}

//获取最新版本
void T3_get_latestVersion(all_T3Check *all_check, char *ret_str) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    char *hosturl = (char *)("w.t3data.net");

    char time_str[16];
	time_t current_time = time(NULL); //时间戳
    sprintf(time_str, "%ld", current_time);
    char *time_encipher_b64 = 加base64((const char *)&time_str, all_check->base64_key);
    if (time_encipher_b64 == NULL) {
        sprintf(ret_str, "err_time_b64数据错误");
        return ;    
    }
    char *time_encipher_Hex = getHEX(time_encipher_b64);
    free(time_encipher_b64);
    time_encipher_b64 = NULL;
    if (time_encipher_Hex == NULL) {
        sprintf(ret_str, "err_time_hex_数据错误");
        return ;        
    }

    char local_timestamp[64] = {0};
    sprintf(local_timestamp, "t=%s", time_encipher_Hex);
    free(time_encipher_Hex);
    time_encipher_Hex = NULL;
    char *http_ret = httppost(hosturl, all_check->版本, local_timestamp);
    if (http_ret != NULL) {
        char *yes_str = 解base64(http_ret, all_check->base64_key);
        free(http_ret);
        http_ret = NULL;   
        if (yes_str != NULL) {
            sprintf(ret_str, "%s", yes_str);
            free(yes_str);
            yes_str = NULL;
        }
    }
}


//获取公告
void T3_get_announcement(all_T3Check *all_check, char *ret_str) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    char *hosturl = (char *)("w.t3data.net");

    char time_str[16];
	time_t current_time = time(NULL); //时间戳
    sprintf(time_str, "%ld", current_time);
    char *time_encipher_b64 = 加base64((const char *)&time_str, all_check->base64_key);
    if (time_encipher_b64 == NULL) {
        sprintf(ret_str, "err_time_b64数据错误");
        return ;    
    }
    char *time_encipher_Hex = getHEX(time_encipher_b64);
    free(time_encipher_b64);
    time_encipher_b64 = NULL;
    if (time_encipher_Hex == NULL) {
        sprintf(ret_str, "err_time_hex_数据错误");
        return ;        
    }

    char local_timestamp[64] = {0};
    sprintf(local_timestamp, "t=%s", time_encipher_Hex);
    free(time_encipher_Hex);
    time_encipher_Hex = NULL;
    char *http_ret = httppost(hosturl, all_check->公告, local_timestamp);
    if (http_ret != NULL) {
        char *yes_str = 解base64(http_ret, all_check->base64_key);
        free(http_ret);
        http_ret = NULL;   
        if (yes_str != NULL) {
            sprintf(ret_str, "%s", yes_str);
            free(yes_str);
            yes_str = NULL;
        }
    }
}

//登陆
int T3_Login(all_T3Check *all_check, int *ret_yes, char *ret_err) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    char *hosturl = (char *)("w.t3data.net");
    //a1
    char *km_key_encipher_b64 = 加base64((const char *)&all_check->km_key_, all_check->base64_key);
    if (km_key_encipher_b64 == NULL) {
        sprintf(ret_err, "err_b64数据错误");
        return 0;    
    }
    char *km_key_encipher_Hex = getHEX(km_key_encipher_b64);
    free(km_key_encipher_b64);
    km_key_encipher_b64 = NULL;
    if (km_key_encipher_Hex == NULL) {
        sprintf(ret_err, "kmkey_hex_数据错误");
        return 0;        
    }
    
    //a2
    char *km_imei_encipher_b64 = 加base64((const char *)&all_check->km_imei_, all_check->base64_key);
    if (km_imei_encipher_b64 == NULL) {
        sprintf(ret_err, "err_b64数据错误");
        return 0;    
    }
    char *km_imei_encipher_Hex = getHEX(km_imei_encipher_b64);
    free(km_imei_encipher_b64);
    km_imei_encipher_b64 = NULL;
    if (km_imei_encipher_Hex == NULL) {
        sprintf(ret_err, "kmimei_hex_数据错误");
        return 0;        
    }

    //a3
    char time_str[16];
	time_t current_time = time(NULL); //时间戳
    sprintf(time_str, "%ld", current_time);
    char *time_encipher_b64 = 加base64((const char *)&time_str, all_check->base64_key);
    if (time_encipher_b64 == NULL) {
        sprintf(ret_err, "err_b64数据错误");
        return 0;    
    }
    char *time_encipher_Hex = getHEX(time_encipher_b64);
    free(time_encipher_b64);
    time_encipher_b64 = NULL;
    if (time_encipher_Hex == NULL) {
        sprintf(ret_err, "time_hex_数据错误");
        return 0;        
    }

    char URLall[4096] = {0};
    sprintf(URLall, "kami=%s&imei=%s&t=%s", km_key_encipher_Hex, km_imei_encipher_Hex, time_encipher_Hex);
    free(km_key_encipher_Hex); km_key_encipher_Hex = NULL;
    free(km_imei_encipher_Hex); km_imei_encipher_Hex = NULL;
    free(time_encipher_Hex); time_encipher_Hex = NULL; 
    char *ret_http_data = httppost(hosturl, all_check->单码, URLall);
    if (ret_http_data == NULL) {
        sprintf(ret_err, "err_T3登录_请求失败!");
        return 0;            
    }
    char *decrypt_http_data = 解base64(ret_http_data, all_check->base64_key);
    free(ret_http_data);
    ret_http_data = NULL;
    if (decrypt_http_data == NULL) {
        sprintf(ret_err, "err_T3登录_请求不完整!");
        return 0;            
    }

    bool ret_int = 0;
    if (strncmp(decrypt_http_data, "登录成功:200;", 14) == 0) {
		char *卡密ID = My_strstrstr(decrypt_http_data, (char *)"卡密ID:", (char *)";");
		char *到期时间 = My_strstrstr(decrypt_http_data, (char *)"到期时间:", (char *)";");
		char *卡密时长 = My_strstrstr(decrypt_http_data, (char *)"卡密时长:", (char *)";");
        char *剩余时间 = My_strstrstr(decrypt_http_data, (char *)"剩余时间:", (char *)";");
        char *校验密钥 = My_strstrstr(decrypt_http_data, (char *)"校验密钥:", (char *)";");
        char *登状态码 = My_strstrstr(decrypt_http_data, (char *)"登录状态码:", (char *)";");
        char *当前时间 = My_strstrstr(decrypt_http_data, (char *)"当前时间:", (char *)";");
        char *当前时戳 = My_strstrstr(decrypt_http_data, (char *)"当前时间戳:", (char *)";");
        char *绑定设备 = My_strstrstr(decrypt_http_data, (char *)"绑定设备:", (char *)";");
        char *解绑次数 = My_strstrstr(decrypt_http_data, (char *)"解绑次数:", (char *)";");
        char *核心数据 = My_strstrstr(decrypt_http_data, (char *)"核心数据:", (char *)";");
        
        bool is_到期时间;
        if (到期时间 == NULL) {
            sprintf(ret_err, "err_T3登录_到期时间获取失败!");     
            is_到期时间 = false;   
        } else {
            sprintf(ret_err, "%s", 到期时间);  
            is_到期时间 = true;    
        }   

         // 保存到期时间到到期时间数组中
        if (is_到期时间) {
            sprintf(到期时间, "%s", 到期时间); // 将到期时间赋值给到期时间数组
        }
        
        if (is_到期时间) {
            char sj[13] = {0};
            My_获取时间戳(sj);
            long vs1 = (long)atoll(sj);
            long vs2 = (long)atoll(当前时间);
            if ((vs1 - vs2) < 18) { 
                *ret_yes = 86001769;  
                ret_int = 1;
    	    } else {
                sprintf(ret_err, "数据过期:请检查本地时间是否正确");
                is_到期时间 = false;
            }
        }
         
           
        if (卡密ID != NULL) { free(卡密ID); 卡密ID = NULL; }           
        if (到期时间 != NULL) { free(到期时间); 到期时间 = NULL; }            
        if (卡密时长 != NULL) { free(卡密时长); 卡密时长 = NULL; }           
        if (剩余时间 != NULL) { free(剩余时间); 剩余时间 = NULL; }
        if (校验密钥 != NULL) { free(校验密钥); 校验密钥 = NULL; }
        if (登状态码 != NULL) { free(登状态码); 登状态码 = NULL; }
        if (当前时间 != NULL) { free(当前时间); 当前时间 = NULL; }
        if (当前时戳 != NULL) { free(当前时戳); 当前时戳 = NULL; }
        if (绑定设备 != NULL) { free(绑定设备); 绑定设备 = NULL; } 
        if (解绑次数 != NULL) { free(解绑次数); 解绑次数 = NULL; } 
        if (解绑次数 != NULL) { free(解绑次数); 解绑次数 = NULL; }
        //char *fg = 解base64(校验密钥, all_check->base64_key);        
        //printf("fg : %s\n", getHEX(fg));
        //if (is_到期时间 == false) {
            //return 0;                    
        //}
    } else {
        sprintf(ret_err, "err_%s", decrypt_http_data);  
        //return 0;            
    }
    //printf("decrypt_http_data : %s\n", decrypt_http_data);
    free(decrypt_http_data);
    decrypt_http_data = NULL;
    return ret_int;
}

int T3_unbind_km(all_T3Check *all_check, char *ret_str) {
    char *hosturl = (char *)("w.t3data.net");
    //a1
    char *km_key_encipher_b64 = 加base64((const char *)&all_check->km_key_, all_check->base64_key);
    if (km_key_encipher_b64 == NULL) {
        sprintf(ret_str, "kmkey_b64数据错误");
        return 0;    
    }
    char *km_key_encipher_Hex = getHEX(km_key_encipher_b64);
    free(km_key_encipher_b64);
    km_key_encipher_b64 = NULL;
    if (km_key_encipher_Hex == NULL) {
        sprintf(ret_str, "kmkey_hex_数据错误");
        return 0;        
    }
    
    //a2
    char time_str[16];
	time_t current_time = time(NULL); //时间戳
    sprintf(ret_str, "%ld", current_time);
    char *time_encipher_b64 = 加base64((const char *)&time_str, all_check->base64_key);
    if (time_encipher_b64 == NULL) {
        sprintf(ret_str, "err_time_b64数据错误");
        return 0;    
    }
    char *time_encipher_Hex = getHEX(time_encipher_b64);
    free(time_encipher_b64);
    time_encipher_b64 = NULL;
    if (time_encipher_Hex == NULL) {
        sprintf(ret_str, "err_time_hex_数据错误");
        return 0;        
    }
    
    
    char local_cs[256] = {0};
    sprintf(local_cs, "kami=%s&t=%s", km_key_encipher_Hex, time_encipher_Hex);
    free(km_key_encipher_Hex); km_key_encipher_Hex = NULL;
    free(time_encipher_Hex); time_encipher_Hex = NULL; 
    
    bool ret_int = 0;
    char *http_ret = httppost(hosturl, all_check->解绑, local_cs);
    if (http_ret != NULL) {
        char *yes_str = 解base64(http_ret, all_check->base64_key);
        free(http_ret);
        http_ret = NULL;   
        if (yes_str != NULL) {
            sprintf(ret_str, "%s", yes_str);
            free(yes_str);
            yes_str = NULL;
            ret_int = 1;
        }
    } else {
        sprintf(ret_str, "解绑 请求失败!");
    }
    
    return ret_int;
}



void T3_Auto(int *yes, char *str_err) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    T3_Gverify_Start:
    
    *yes = 0; 
    char *android_id = NULL;
    char Kami[128] = {0};   
    char *key_path = (char *)("/storage/emulated/0/蔓延验证");   
    loadConfig_ver(key_path, (void *)Kami, sizeof(Kami));
    if (strlen(Kami) == 0) {
        printf("请输入卡密(随便输入) : \n");
        scanf("%s", Kami);  // 从标准输入流中读取字符串    
        if (strlen(Kami) == 0) {
            printf("错误\n");
            goto T3_Gverify_Start;
    		//exit(1); 	
        } 
    } else {
        printf("\n已检测到卡密 自动登录中\n");
    }


    if (android_id == NULL || strlen(android_id) == 0) {
        char *local_ret = my_shell("settings get secure android_id");
        if (strlen(local_ret) > 4) {
            local_ret[strlen(local_ret)-1] = '\0'; //去掉换行
            android_id = local_ret;
            //printf("android_id : %s (%d)\n", android_id, strlen(local_ret));
        } else {
            free(local_ret);
            local_ret = NULL;
        }
    }
    if (android_id == NULL || strlen(android_id) == 0) {
        android_id = getAndroid_boot_serialno();
    }    
    if (android_id == NULL || strlen(android_id) == 0) {
        printf("设备码获取错误\n");
        exit(0);    
    }   

    all_T3Check *local_CheckData = get_all_T3CheckData();
    snprintf(local_CheckData->km_imei_, sizeof(local_CheckData->km_imei_), "%s", android_id);
    snprintf(local_CheckData->km_key_, sizeof(local_CheckData->km_key_), "%s", Kami);
    int ret_it = ::T3_Login(local_CheckData, (int *)yes, (char *)str_err);
    if (ret_it == 1 && *yes == 86001769) {
        saveConfig_ver(key_path, (void *)Kami, strlen(Kami));
    } else {
     	remove(key_path);
        printf("登陆状态 : %s\n", str_err);   
        goto T3_Gverify_Start;    
    }
}

all_T3Check* get_all_T3CheckData() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
	static all_T3Check *local_T3_verify = (all_T3Check *) calloc(1, sizeof(all_T3Check));
	if (strlen(local_T3_verify->单码) == 0) {
    	snprintf(local_T3_verify->单码, sizeof(local_T3_verify->单码), "%s", "A4892FDF2802F7F4");
      	snprintf(local_T3_verify->解绑, sizeof(local_T3_verify->解绑), "%s", "1897B569D1A94DC4");
       	snprintf(local_T3_verify->公告, sizeof(local_T3_verify->公告), "%s", "7A40F7585C1BA751");
     	snprintf(local_T3_verify->版本, sizeof(local_T3_verify->版本), "%s", "55F61B64264D9676");
     	snprintf(local_T3_verify->base64_key, sizeof(local_T3_verify->base64_key), "%s", "/bWjAxqe1Q0PH5IwkKiOmvop2FDf+XgZBaNhun64MrsLl3cC7dJzU9S8VTyEtRYG");
    }
    return local_T3_verify;
}
