#include "imgui.h"
#include "图片调用.h"
#include "辅助类.h"
#include <linux/input.h>
#include <fcntl.h>
#include <unistd.h>
#include "common.h"
//bool 悬浮窗 = false; // 实际定义
int 数据() {
    DIR *dir = opendir("/dev/input/");
    dirent *ptr = NULL;
    int count = 0;
    while ((ptr = readdir(dir)) != NULL) {
        if (strstr(ptr->d_name, "event"))
            count++;
    }
    return count ? count : -1;
}

int 音量() {
    int EventCount = 数据();
    if (EventCount < 0) {
        printf("未找到输入设备\n");
        return -1;
    }

    int *fdArray = (int *)malloc(EventCount * sizeof(int));

    for (int i = 0; i < EventCount; i++) {
        char temp[128];
        sprintf(temp, "/dev/input/event%d", i);
        fdArray[i] = open(temp, O_RDWR | O_NONBLOCK);
    }

    input_event ev;
    int count = 0; // 记录按下音量键的次数

    while (1) {
        for (int i = 0; i < EventCount; i++) {
            memset(&ev, 0, sizeof(ev));
            read(fdArray[i], &ev, sizeof(ev));
            if (ev.type == EV_KEY && (ev.code == KEY_VOLUMEUP || ev.code == KEY_VOLUMEDOWN)) {
                if (ev.code == 115&&ev.value==1) { //音量➕
                    悬浮窗 = true;
                } else if (ev.code == 114&&ev.value==1) {
                    悬浮窗 = false;
                }
            }
            usleep(1000);
        }
        usleep(500);
    }
    usleep(1500);
    return 0;
}
char folder_path[] = "/data/猎鹰判断/";
bool 窗口;

bool 猎鹰巡查 = false; // 初始设置为
bool FSyfsbl = false;

//⭐猎鹰文件生成
#include <iostream>
#include <fstream>
#include <ctime>
#include <cstdlib>
#include <unistd.h>
#include <thread>

void 猎鹰(){
while (true) {
        srand(time(0));
        int random_number = rand() % 100;

        if (random_number < 1) {
            std::ofstream file("/data/猎鹰判断/监测到猎鹰巡查正在观战...");
            if (file.is_open()) {
                file.close();
                std::cout << " " << std::endl;
            } else {
                std::cout << " " << std::endl;
            }
        } else {
            std::cout << " " << std::endl;
        }

        sleep(500); // 每500s循环一次
    }

}
extern 绘制 绘制;
bool 悬浮窗 = false;
static bool 悬浮球 = true;
static bool 自瞄控件 = false;
static int style_idx = 0;
// ImVec2 Pos; //窗口位置
ImVec2 Pos2;
ImVec2 windowSize, windowSize_max;
static bool IsBall = false;
static bool 窗口状态 = false;
static bool show_ChildMenu1 = true;
static bool show_ChildMenu2 = false;
static bool show_ChildMenu3 = false;
static bool show_ChildMenu4 = false;
static bool show_ChildMenu5 = false;
static int 录屏时长 = 600; // 默认时间为10秒
static std::atomic<bool> isRecording(false); // 全局变量
static std::atomic<int> recordingTimeElapsed(0); // 录制经过的时间
static int 自瞄窗口 = 0;
static int 配置窗口 = 0;
static int 物资窗口 = 0;
static bool 广角设置 = false;
static bool 跳伞滞空 = false;
static std::atomic<bool> 跳伞滞空线程运行中(false);

std::string GetAndroidVersion()
{
  char value[256];
  __system_property_get("ro.build.version.release", value);
  return std::string(value);
}
ImU32 c_透明 = IM_COL32(0, 0, 0, 0);
ImU32 c_fafafa = IM_COL32(250, 250, 250, 255);
ImU32 c_cccccc = IM_COL32(204, 204, 204, 255);
ImU32 c_c2c2c2 = IM_COL32(194, 194, 194, 255);
ImU32 c_23292e = IM_COL32(35, 41, 46, 255);
ImU32 c_4023292e = IM_COL32(35, 41, 46, 125);
ImU32 c_eeeeee = IM_COL32(0xee, 0xee, 0xee, 255);
ImU32 c_2f363c = IM_COL32(47, 54, 60, 255);
ImU32 c_402f363c = IM_COL32(47, 54, 60, 125);
ImU32 c_DAB123 = IM_COL32(218, 177, 35, 255);
ImU32 c_DCD4CA = IM_COL32(220, 212, 202, 255);
ImU32 c_2C88E2 = IM_COL32(44, 136, 226, 255);
ImU32 c_2C88E240 = IM_COL32(44, 136, 226, 128);
ImU32 c_545D6D = IM_COL32(84, 93, 109, 255);
ImU32 c_545D6D40 = IM_COL32(84, 93, 109, 128);
ImU32 c_FFD49F = IM_COL32(255, 212, 159, 255);
ImU32 c_FFD49F80 = IM_COL32(255, 212, 159, 128);
ImU32 c_16b777 = IM_COL32(0, 0, 0, 0); // 透明
ImU32 c_16b77780 = IM_COL32(0x16, 0xB7, 0x77, 128);
ImU32 c_11243B = IM_COL32(0x11, 0x24, 0x3B, 255);
ImU32 c_11243B80 = IM_COL32(0x11, 0x24, 0x3B, 128);
ImU32 c_31bdec = IM_COL32(0x31, 0xbd, 0xec, 255);
ImU32 c_558FFD = IM_COL32(0x55, 0x8F, 0xFD, 255);
ImU32 c_81A4DC = IM_COL32(0x81, 0xa4, 0xdc, 255);
ImVec4 透明ImVec4 = ImVec4(0, 0, 0, 0);
ImVec4 黑色ImVec4 = ImVec4(0, 0, 0, 1.0f);
ImVec4 灰色ImVec4 = ImVec4(0.1f, 0.1f, 0.1f, 1.0f);
ImVec4 灰色ImVec440 = ImVec4(0.1f, 0.1f, 0.1f, 0.5f);
ImVec4 灰色ImVec480 = ImVec4(0.1f, 0.1f, 0.1f, 0.75f);
ImVec4 亮灰色ImVec4 = ImVec4(0.5, 0.5, 0.5, 1);
ImVec4 亮黄色ImVec4 = ImVec4(1.0f, 0.831f, 0.623f, 1.0f);
ImVec4 半透明黑色ImVec4 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.5f);
ImVec4 半透明黑色ImVec480 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.75f);
ImVec4 ImVec423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 1.0f);
ImVec4 ImVec80423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.75f);
ImVec4 ImVec40423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.5f);
ImVec4 白色ImVec4 = ImVec4(0.5, 0.5, 0.5, 0.1);
ImVec4 ImVec4fafafa = ImVec4(0xfa / 255.0f, 0xfa / 255.0f, 0xfa / 255.0f, 1.0f);
ImVec4 ImVec431bdec = ImVec4(0x31 / 255.0f, 0xbd / 255.0f, 0xec / 255.0f, 1.0f);
ImVec4 ImVec416b777 = ImVec4(0.0f, 0.0f, 0.0f, 0.0f); // 透明
ImVec4 ImVec4c2c2c2 = ImVec4(0xC2 / 255.0f, 0xC2 / 255.0f, 0xC2 / 255.0f, 1.0f);
ImVec4 ImVec42C88E2 = ImVec4(0x2C / 255.0f, 0x88 / 255.0f, 0xE2 / 255.0f, 1.0f);
ImVec4 ImVec411243B = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 1.0f);
ImVec4 ImVec411243B80 = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 0.75f);
ImVec4 ImVec411243B40 = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 0.5f);
ImVec4 ImVec4f3c258 = ImVec4(0xf3 / 255.0f, 0xc2 / 255.0f, 0x58 / 255.0f, 1.0f);
ImVec4 ImVec4eeeeee = ImVec4(0xee / 255.0f, 0xee / 255.0f, 0xee / 255.0f, 1.0f);
ImVec4 ImVec4558FFD = ImVec4(0x55 / 255.0f, 0x8f / 255.0f, 0xff / 255.0f, 1.0f);
ImVec4 ImVec481A4DC = ImVec4(0x81 / 255.0f, 0xa4 / 255.0f, 0xdc / 255.0f, 1.0f);

void CustomNewLine(float lineHeight = -1.0f) {
    if (lineHeight < 0.0f) {
        // 使用默认的行高
        ImGui::NewLine();
    } else {
        // 设置自定义行高
        ImGui::Dummy(ImVec2(0.0f, lineHeight));
    }
}
void 跳伞滞空线程函数() {
    while (跳伞滞空线程运行中.load()) {
        long oneself = 绘制.地址.自身地址; // 直接用全局自身地址
        long libue4 = 绘制.地址.libue4;

        long oneselfAngle = libue4 + 0x11A05534;
        long parachute = oneself + 0x2348;
        long parachuteSpeed = 绘制.读写.getPtr64(oneself + 0x23C0) + 0x21C;

        if (绘制.读写.getDword(oneselfAngle) == 1) {
            if (绘制.读写.getDword(parachute) == 16777728) {
                绘制.读写.WriteFloat(parachuteSpeed, 0.0f);
            }
        }
        usleep(10 * 1000); // 10ms 高频写入
    }
}
void RadioGroup(const char* label, int* selectedIndex, const char* options[], int numOptions, float width, float fontSize = 1.0f, float padding = 10.0f) {
    ImGui::SetWindowFontScale(fontSize);
    ImGui::PushID(label);
    const ImGuiStyle& style = ImGui::GetStyle();
    const ImVec2 startPos = ImGui::GetCursorScreenPos();
    const float buttonSpacing = style.ItemSpacing.x;
    const float borderRadius = 10.0f;
    const ImVec2 buttonStartPos = ImVec2(startPos.x + padding, startPos.y + padding + style.FramePadding.y);
    const ImU32 selectedColor = c_558FFD;
    const ImU32 unselectedTextColor = c_4023292e;
    const ImU32 selectedTextColor = c_fafafa;
    const float buttonWidth = (width - buttonSpacing * (numOptions - 1)) / numOptions;
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, borderRadius);
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0.0f, style.ItemSpacing.y));
    float totalWidth = 0.0f;
    for (int i = 0; i < numOptions; ++i) {
        totalWidth += buttonWidth;
    }
    const ImVec2 bgSize(totalWidth + padding * 2.0f, style.FramePadding.y * 2.0f + ImGui::GetTextLineHeight() + padding * 2.0f);
    const ImVec2 bgStartPos = ImVec2(startPos.x - padding, startPos.y - padding);
    const ImU32 shadowColor = IM_COL32(0, 0, 0, 64);
    const ImVec2 shadowOffset(3.0f, 3.0f);
    const ImVec2 shadowStartPos = ImVec2(bgStartPos.x + shadowOffset.x, bgStartPos.y + shadowOffset.y);
    const ImVec2 shadowEndPos = ImVec2(shadowStartPos.x + bgSize.x, shadowStartPos.y + bgSize.y);
    // ImGui::GetWindowDrawList()->AddRectFilled(shadowStartPos, shadowEndPos, shadowColor, style.FrameRounding);
    ImGui::GetWindowDrawList()->AddRectFilled(bgStartPos, ImVec2(bgStartPos.x + bgSize.x, bgStartPos.y + bgSize.y), c_eeeeee, style.FrameRounding);
    for (int i = 0; i < numOptions; ++i) {
        if (i > 0) {
            ImGui::SameLine();
        }
        if (*selectedIndex == i) {
            ImGui::PushStyleColor(ImGuiCol_Button, selectedColor);
            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, selectedColor);
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, selectedColor);
            ImGui::PushStyleColor(ImGuiCol_Text, selectedTextColor);
        } else {
            ImGui::PushStyleColor(ImGuiCol_Button, IM_COL32(0, 0, 0, 0));         // 透明
            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, IM_COL32(0, 0, 0, 0));  // 透明
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, IM_COL32(0, 0, 0, 0));   // 透明
            ImGui::PushStyleColor(ImGuiCol_Text, unselectedTextColor);
        }
        if (ImGui::Button(options[i], ImVec2(buttonWidth, 0.0f))) {
            *selectedIndex = i;
        }
        ImGui::PopStyleColor(4);
    }
    ImGui::PopStyleVar(2);
    ImGui::PopStyleColor();
    ImGui::PopID();
    ImGui::SetWindowFontScale(1.0f);
}

void CustomText(const char* text, ImVec2 size, int padding = 5, int alignment = 0, float fontSize = 1.5f, bool showShadow = false, ImVec4 bgColor = ImVec4(0.0f, 0.0f, 0.0f, 0.5f), ImVec4 textColor = ImVec40423292e, ImVec4 shadowColor = ImVec4(0.0f, 0.0f, 0.0f, 0.5f)) {
    ImGui::SetWindowFontScale(fontSize);
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return;
    const ImGuiStyle& style = ImGui::GetStyle();
    const ImGuiID id = window->GetID(text);
    const ImVec2 contentMin = window->DC.CursorPos;
    const ImVec2 contentMax = ImVec2(contentMin.x + size.x, contentMin.y + size.y);
    if (showShadow) {
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImU32 shadowColor2 = IM_COL32(0, 0, 0, 128);
        float shadowStartY = contentMin.y + size.y - 3;
        float shadowEndY = shadowStartY + 5;
        drawList->AddRectFilledMultiColor(
            ImVec2(contentMin.x, shadowStartY),
            ImVec2(contentMin.x + size.x, shadowEndY),
            shadowColor2, shadowColor2, shadowColor2, shadowColor2);
    }
    if (bgColor.w > 0.0f) {
        window->DrawList->AddRectFilled(contentMin, contentMax, ImGui::GetColorU32(bgColor));
    }
    ImVec2 textSize = ImGui::CalcTextSize(text);
    ImVec2 textPos;
    switch (alignment) {
        case 1:
            textPos = ImVec2(contentMax.x - textSize.x - padding, contentMin.y + (size.y - textSize.y) * 0.5f);
            break;
        case 2:
            textPos = ImVec2(contentMin.x + (size.x - textSize.x) * 0.5f, contentMin.y + (size.y - textSize.y) * 0.5f);
            break;
        default:
            textPos = ImVec2(contentMin.x + padding, contentMin.y + (size.y - textSize.y) * 0.5f);
            break;
    }
    window->DrawList->AddText(textPos, ImGui::GetColorU32(textColor), text);
    float textHeightWithPadding = textSize.y + padding * 2;
    ImGui::ItemSize(ImVec2(size.x, textHeightWithPadding), style.FramePadding.y);
    if (!ImGui::ItemAdd(ImRect(contentMin, ImVec2(contentMax.x, contentMin.y + textHeightWithPadding)), id))
        return;
    ImGui::SetWindowFontScale(1.0f);
}

void 绘制背景图(TextureInfo textureInfo, ImVec2 center, float size) {
    ImGui::SetCursorPos({0, 180});
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    draw_list->AddImage(textureInfo.textureId, {center.x - size / 2, center.y - size / 2}, {center.x + size / 2, center.y + size / 2});
}

void 布局::绘制悬浮窗() {
    drawBegin();

    if (绘制.按钮.绘制) {
        绘制.运行绘制();
    }
if (广角设置)
    {
        long 广角地址 = 绘制.读写.getPtr64(绘制.地址.自身地址 + 0x4d8);
        绘制.读写.WriteFloat(绘制.读写.getPtr64(广角地址 + 0x8) + 0x2F4, 绘制.按钮.第三人称);
    //    绘制.读写.WriteFloat(绘制.读写.getPtr64(广角地址 + 0xC0) + 0x2F4, 绘制.按钮.第一人称);
        //绘制.读写.WriteFloat(绘制.读写.getPtr64(广角地址 + 0x50) + 0x2F4, 绘制.按钮.开镜广角);
    }
    

    if (绘制.自瞄.自瞄控件) {
        ImGui::SetNextWindowSize({200, 200});
        if (ImGui::Begin("自瞄控件", &自瞄控件, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar)) {
            auto Pos = ImGui::GetWindowPos();

            static bool isMouseDown = false;
            static bool wasMousePressed = false;
            static bool isTe = false;
            static ImVec2 mousePressPos;
            bool mouseDown = ImGui::IsMouseDown(ImGuiMouseButton_Left);
            bool windowHovered = ImGui::IsWindowHovered();

            if (mouseDown && !isMouseDown && windowHovered && ImGui::IsMouseHoveringRect(Pos, {Pos.x + 100, Pos.y + 100})) {
                isMouseDown = true;
                wasMousePressed = true;
                mousePressPos = ImGui::GetMousePos();
            } else if (!mouseDown && isMouseDown && wasMousePressed) {
                ImVec2 mouseReleasePos = ImGui::GetMousePos();
                if (mousePressPos.x == mouseReleasePos.x && mousePressPos.y == mouseReleasePos.y) {
                    isTe = !isTe;
                }

                isMouseDown = false;
                wasMousePressed = false;
            }

            if (isTe) {
                绘制.自瞄.初始化 = true;
                ImGui::GetWindowDrawList()->AddImage(手持图片[4].textureId, {Pos.x + 20, Pos.y + 20}, {Pos.x + 120, Pos.y + 120});
            } else {
                绘制.自瞄.初始化 = false;
                ImGui::GetWindowDrawList()->AddImage(手持图片[3].textureId, {Pos.x + 20, Pos.y + 20}, {Pos.x + 120, Pos.y + 120});
            }
        }
        ImGui::End();
    }

    if (悬浮球) {
        ImGui::SetNextWindowSize({200, 200});
        if (ImGui::Begin("悬浮图片", &悬浮球, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar)) {
            绘制.winWidth = ImGui::GetWindowWidth();
            绘制.winHeith = ImGui::GetWindowHeight();
            if (窗口状态) {
                ImGui::SetWindowPos("悬浮图片", 绘制.Pos, ImGuiCond_Always);
                窗口状态 = false;
            }
            绘制.Pos = ImGui::GetWindowPos();
            ImGui::GetWindowDrawList()->AddImage(手持图片[60].textureId, {绘制.Pos.x + 20, 绘制.Pos.y + 20}, {绘制.Pos.x + 120, 绘制.Pos.y + 120});


            static bool isDragging = false;
            if (ImGui::IsItemActive()) {
                if (!isDragging) {
                    isDragging = true;
                    Pos2 = ImGui::GetWindowPos();
                }
            } else if (isDragging) {
                isDragging = false;
                if (Pos2.x == ImGui::GetWindowPos().x && Pos2.y == ImGui::GetWindowPos().y) {
                    悬浮球 = false;
                    悬浮窗 = true;
                    窗口状态 = true;
                    IsBall = true;
                    ImGui::SetWindowPos(绘制.悬浮窗标题, 绘制.Pos, ImGuiCond_Always);
                }
            }
        }
        ImGui::End();
    }

if (悬浮窗)
{
    // 添加雪花控制变量（静态变量确保状态保持）
    static bool showSnow = false; // 控制雪花显示

    ImGuiStyle &style = ImGui::GetStyle();
    style.WindowRounding = 30.0f;
    ImVec2 minSize = ImVec2(1000, 1100);
    ImVec2 maxSize = ImVec2(FLT_MAX, FLT_MAX);
    ImGui::SetNextWindowSizeConstraints(minSize, maxSize);
    
    绘制.Pos = ImGui::GetWindowPos();

    // 使用完全透明背景，因为雪花会自己绘制背景
    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Begin("老王开发V-WDN007007", &悬浮窗, 
        ImGuiWindowFlags_NoCollapse | 
        ImGuiWindowFlags_NoResize | 
        ImGuiWindowFlags_NoTitleBar))
    {
        // ================= 雪花粒子系统 =================
        if (showSnow) {
            static std::vector<ImVec2> snowflakes;
            static bool snowInitialized = false;
            
            ImVec2 windowPos = ImGui::GetWindowPos();
            ImVec2 windowSize = ImGui::GetWindowSize();
            ImDrawList* drawList = ImGui::GetWindowDrawList();
            
            // 绘制半透明背景
            drawList->AddRectFilled(windowPos, 
                                    ImVec2(windowPos.x + windowSize.x, windowPos.y + windowSize.y), 
                                    IM_COL32(0, 0, 0, 180), 
                                    30.0f);
            
            // 初始化雪花
            if (!snowInitialized || snowflakes.size() != 200) {
                snowInitialized = true;
                snowflakes.resize(200); // 200个雪花
                for (auto& flake : snowflakes) {
                    flake.x = rand() % (int)windowSize.x;
                    flake.y = rand() % (int)windowSize.y;
                }
            }
float deltaTime = ImGui::GetIO().DeltaTime;
for (auto& flake : snowflakes) {
    // 更新位置（Y轴下落，X轴轻微摆动）
    flake.y += 60.5 * deltaTime; // 下落速度
    
    // 修正括号错误：移除了多余的右括号
    flake.x += sin(ImGui::GetTime() * 0.5f + flake.x) * 10.0f * deltaTime; // 摆动效果
    
    // 边界检查（从顶部重新出现）
    if (flake.y > windowSize.y) {
        flake.y = -5;
        flake.x = rand() % (int)windowSize.x;
    }
    if (flake.x < -15) flake.x = windowSize.x + 15;
    if (flake.x > windowSize.x + 15) flake.x = -15;
    
    // 绘制雪花（不同大小增强层次感）
    // 修正括号错误：移除了多余的左括号
    float size = 3.5f + sin(flake.x * 0.1f) * 1.5f;
    drawList->AddCircleFilled(
        ImVec2(windowPos.x + flake.x, windowPos.y + flake.y), 
        size, 
        IM_COL32(255, 255, 255, 200)
    );
}

        } else {
            // 不显示雪花时绘制默认半透明背景
            ImVec2 windowPos = ImGui::GetWindowPos();
            ImVec2 windowSize = ImGui::GetWindowSize();
            ImDrawList* drawList = ImGui::GetWindowDrawList();
            drawList->AddRectFilled(windowPos, 
                                    ImVec2(windowPos.x + windowSize.x, windowPos.y + windowSize.y), 
                                    IM_COL32(0, 0, 0, 180), 
                                    30.0f);
        }
        // ================= 雪花系统结束 =================
             std::string androidVersion = GetAndroidVersion();
ImGuiStyle& style = ImGui::GetStyle();
style.WindowRounding = 10.0f;
if (窗口)
{
ImVec4 originalWindowBg = ImGui::GetStyle().Colors[ImGuiCol_WindowBg];
ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(1.00f, 1.00f, 1.00f, 1.00f));
ImGui::SetNextWindowSize({220, 120});
ImGui::Begin("猎鹰提示", &FSyfsbl, ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar);
ImGui::SetWindowFontScale(1.2f);
if (std::filesystem::exists("/data/猎鹰判断/监测到猎鹰巡查正在观战...")) {
ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "监测到猎鹰巡查正在观战...");
} else {
ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.5f, 0.7f, 0.9f, 1.0f)); // 纯绿色);
ImGui::Text("没有被巡查");
ImGui::PopStyleColor();
}
ImGui::SetWindowFontScale(1.0f);
ImGui::End();
ImGui::PopStyleColor();
}
        static bool 窗口状态 = false;
        
        // 关闭按钮
        ImGui::SetCursorPosX(ImGui::GetWindowWidth() - 100);
        ImGui::SetCursorPosY(5);
        if (ImGui::Button("关闭", ImVec2(100, 50))) {
            悬浮窗 = false;
        }
        
        // 标题
        ImVec2 文本大小 = ImGui::CalcTextSize("卡卡内核", NULL, true);
        ImGui::SetCursorPosY(6);
        ImGui::SetCursorPosX(6);
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
        ImGui::TextWrapped("   卡卡内核   ");
        ImGui::PopStyleColor(); 

        绘制.Pos = ImGui::GetWindowPos();
        ImVec2 imagePos = ImVec2(绘制.Pos.x + 0, 绘制.Pos.y + 0);
        ImVec2 imageSize = ImVec2(1650, 1290);
        static int selectedMenu = 3;

        ImVec4 originalBorderCol = ImGui::GetStyle().Colors[ImGuiCol_Border];
        ImGui::GetStyle().Colors[ImGuiCol_Border] = originalBorderCol;
        ImGui::GetStyle().Colors[ImGuiCol_Border] = ImVec4(0.0f, 1.0f, 0.0f, 0.3f); 

        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 1.0f, 0.0f, 1.0f));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(33, 5));
        ImGui::BeginChild("##菜单", ImVec2(950, 1000), false, 
                        ImGuiWindowFlags_NoSavedSettings | 
                        ImGuiWindowFlags_AlwaysAutoResize | 
                        ImGuiWindowFlags_NoScrollbar);

        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.0f, 0.0f, 1.0f));       
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));        

        // ========== 第一排：6个按钮 ========== 
        float firstRowStartY = ImGui::GetCursorPosY();
        if (ImGui::Button("主页", ImVec2(130, 50)))   { selectedMenu = 3; } ImGui::SameLine();
        if (ImGui::Button("绘制", ImVec2(130, 50)))   { selectedMenu = 0; } ImGui::SameLine();
        if (ImGui::Button("自瞄", ImVec2(130, 50)))   { selectedMenu = 1; } ImGui::SameLine();
        if (ImGui::Button("物资", ImVec2(130, 50)))   { selectedMenu = 2; } ImGui::SameLine();
        if (ImGui::Button("颜色", ImVec2(130, 50)))   { selectedMenu = 4; } ImGui::SameLine();
        if (ImGui::Button("粗细", ImVec2(130, 50)))   { selectedMenu = 5; } 

        // ========== 第二排：2个按钮 ========== 
        float rowSpacing = 6.0f;
        float secondRowStartY = firstRowStartY + 60 + rowSpacing;
        ImGui::SetCursorPosY(secondRowStartY);
        if (ImGui::Button("主菜单", ImVec2(130, 50)))   { selectedMenu = 3; } ImGui::SameLine();
        if (ImGui::Button("内存功能", ImVec2(140, 50)))   { selectedMenu = 7; } 

        ImGui::PopStyleColor(2);  
        ImGui::PopStyleVar();
        ImGui::Spacing();     
        ImGui::Spacing();
        ImGui::Spacing();
        ImGui::Spacing();
        
        switch (selectedMenu)
        {
        case 3:
        {
            ImGui::BeginChild("##首页", ImVec2(-1, -1), false, 
                            ImGuiWindowFlags_NoSavedSettings | 
                            ImGuiWindowFlags_AlwaysAutoResize);
                            // ... 保留原有按钮和其他控件的代码 ...

        // 添加显示北京时间的代码
        {
    // 获取当前时间
    std::time_t now = std::time(nullptr);
    char timeStr[64];
    std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", std::localtime(&now));
    
    // 设置时间显示样式
    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 10); // 调整位置
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f)); // 设置文字颜色
    ImGui::Text("服务器守护北京时间: %s", timeStr);
    ImGui::PopStyleColor();
        }
            
            const ImVec2 btnSize(470, 60); 
            ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(20, 15)); 
            
            if (ImGui::Button("载入程序", btnSize))
            {
                绘制.初始化绘制("com.tencent.tmgp.pubgmhd", abs_ScreenX, abs_ScreenY);
                绘制.按钮.绘制 = true;
            }
            ImGui::SameLine();
            if (ImGui::Button("安全退出", btnSize)) 
            { 
                exit(1); 
            }
            ImGui::SameLine(); 
            ImGui::NewLine();
            
            if (ImGui::Button("漏手模式", btnSize)) 
            { 
                绘制.按钮.人数 = false;
                绘制.按钮.方框 = false;
                绘制.按钮.血量 = false;
                绘制.按钮.手持 = false;
                绘制.按钮.盒子 = false;
                绘制.按钮.背敌预警 = false;
                绘制.按钮.距离 = false;
                绘制.按钮.射线 = false;
                绘制.按钮.名字 = false;
                绘制.按钮.骨骼 = false;
                绘制.按钮.车辆 = false;
                绘制.按钮.雷达 = false;
                绘制.按钮.载具血量 = false;
                绘制.按钮.载具油量 = false;
                绘制.按钮.手雷预警 = false;    
                绘制.自瞄.自瞄控件 = false;
                绘制.自瞄.准星射线 = false;
                绘制.自瞄.隐藏自瞄圈 = true;
                悬浮球 = false;
            }
            ImGui::SameLine(); 
            
            // 雪花开关按钮
            if (ImGui::Button(showSnow ? "关闭雪花" : "开启雪花", btnSize)) 
            { 
                showSnow = !showSnow; // 切换雪花状态
            }

if (猎鹰巡查)
{
if (ImGui::Button("关闭猎鹰检测", {470,60}))
{
窗口 = false;
猎鹰巡查 = false;
}
} else {
if (ImGui::Button("开启猎鹰检测", {470,60}))
{

窗口 = true;
猎鹰巡查 = true;
}
}           



ImGui::PopStyleVar(); // 恢复间距样式，避免影响后续UI
        ImGui::Spacing();     
        ImGui::Spacing();
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
        ImGui::BulletText("·如果自瞄想锁一点平滑速度11，预判1.20，压枪自行调节"); 
        ImGui::BulletText("·打太猛或不演戏会高风险"); 
        ImGui::BulletText("·公益版本倒卖死全家"); 
        ImGui::BulletText("·远点尽量少抽，类似于一梭子摁到底"); 
        ImGui::BulletText("·尽量避免打墙"); 
        ImGui::BulletText("·别扫车扫马太准，远点就别扫了"); 
        ImGui::BulletText("·别穿烟穿草抓无视野无脚步精准抓人包括人机");
        ImGui::Spacing();     
        ImGui::Spacing();
        ImGui::Spacing();               
        ImVec4 colorNormal = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);                  // 绿色
        ImVec4 colorAbnormal = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);                // 红色
        ImVec4 colorWhite = ImVec4(1.0f, 1.0f, 0.0f, 1.0f);                   // 白色
        ImGui::Separator();                                                   // 横线
        std::string androidVersion = GetAndroidVersion();
        ImGui::Text("当前Android版本: 安卓%s", androidVersion.c_str());
        ImGui::PopStyleColor();
        ImGui::SameLine(0, 150); // 在同一行显示
        ImGui::TextColored(colorWhite, "当前状态: ");
        if (绘制.地址.libue4)
        {
          ImGui::SameLine(); // 在同一行显示
          ImGui::TextColored(colorNormal, "安全运行");
        }
        else
        {
          ImGui::SameLine(); // 在同一行显示
          ImGui::TextColored(colorAbnormal, "异常运行");
        }
         ImGui::EndChild();
        break;
      }

      case 0:
      {
        ImGui::BeginChild("##绘制", ImVec2(-1, -1), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        static int DrawMenu = 0;

        ImGui::BeginChild("##绘制菜单", ImVec2(-1, 75), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        if (ImGui::Button("主绘制"))
        {
          DrawMenu = 0;
        }
        ImGui::SameLine();
        if (ImGui::Button("绘制调节"))
        {
          DrawMenu = 2;
        }
        ImGui::SameLine();
        if (ImGui::Button("屏幕录制"))
        {
          DrawMenu = 3;
        }
        ImGui::SameLine();
        if (ImGui::Button("绘制样式"))
        {
          DrawMenu = 4;
        }
        ImGui::EndChild();

        switch (DrawMenu)
        {
        case 0:
        {
          ImGui::SameLine();
          static bool 全部开启 = false;
          if (全部开启)
          {
            绘制.按钮.人数 = true;
            绘制.按钮.方框 = true;
            绘制.按钮.血量 = true;
            绘制.按钮.距离 = true;
            绘制.按钮.射线 = true;
            绘制.按钮.名字 = true;
            绘制.按钮.骨骼 = true;
            绘制.按钮.手持 = true;
            绘制.按钮.车辆 = true;
            绘制.按钮.手雷预警 = true;
            绘制.按钮.盒子 = true;
            绘制.按钮.背敌预警 = true;
          }
          ImGui::Separator();
          if (ImGui::Checkbox("顶部人数       ", &绘制.按钮.人数))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("方框开关       ", &绘制.按钮.方框))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("血量显示       ", &绘制.按钮.血量))
            绘制.保存配置();
          
          if (ImGui::Checkbox("手持枪械       ", &绘制.按钮.手持))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("盒子开关       ", &绘制.按钮.盒子))
            绘制.保存配置();
          ImGui::SameLine();         
          if (ImGui::Checkbox("背敌开关       ", &绘制.按钮.背敌预警))
            绘制.保存配置();
          
          if (ImGui::Checkbox("距离开关       ", &绘制.按钮.距离))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("射线开关       ", &绘制.按钮.射线))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("名字开关       ", &绘制.按钮.名字))
            绘制.保存配置();
          
          if (ImGui::Checkbox("骨骼开关       ", &绘制.按钮.骨骼))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("雷达开关       ", &绘制.按钮.雷达))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("手雷预警       ", &绘制.按钮.手雷预警))
            绘制.保存配置();
          
          if (ImGui::Checkbox("车辆显示       ", &绘制.按钮.车辆))
            绘制.保存配置();
           ImGui::SameLine();
          if (ImGui::Checkbox("忽略人机       ", &绘制.按钮.忽略人机))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("空投显示       ", &绘制.按钮.空投))
            绘制.保存配置();
          
            
            
          break;
        }
        case 3:
        {
if (ImGui::Button("开启录制", ImVec2(160, 70))) {
        if (!isRecording.load()) {
            isRecording.store(true);
            recordingTimeElapsed.store(0); // 开始时重置时间
            std::thread([](int 录屏时长) {
                // 非阻塞式开始录制
                std::string command = "screenrecord --time-limit " + std::to_string(录屏时长) + " /sdcard/video.mp4 &";
                system(command.c_str());

                for (int i = 0; i < 录屏时长 && isRecording.load(); ++i) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    recordingTimeElapsed.fetch_add(1);
                }

                isRecording.store(false);
            }, 录屏时长).detach();
        }
    }
ImGui::SameLine();
    if (ImGui::Button("停止录制", ImVec2(160, 70)) && isRecording.load()) {
        system("pkill -l SIGINT screenrecord");
        isRecording.store(false);
    }
    ImGui::SameLine();
    if (ImGui::Button("点击截屏", {160, 70})) {
        const char* directoryPath = "/storage/emulated/0/SkyGod截屏";
        struct stat info;
        if (stat(directoryPath, &info) != 0) {
            int result = mkdir(directoryPath, 0777);
            if (result == 0) {
                // 目录创建成功
            } else {
                // 目录创建失败
            }
        }

        int fileCounter = 1;
        std::string filePath = std::string(directoryPath) + "/屏幕截图";
        while (true) {
            std::string filename = filePath + std::to_string(fileCounter) + ".png";
            struct stat buffer;
            if (stat(filename.c_str(), &buffer) == 0) {
                fileCounter++;
            } else {
                char command[5000];
                sprintf(command, "su -c 'screencap %s' &", filename.c_str());
                system(command); // 执行截屏命令
                break;
            }
        }
    }

    // 恢复默认样式
    ImGui::PopStyleVar(); // 恢复 FrameRounding 设置

    if (isRecording.load()) {
        int elapsed = recordingTimeElapsed.load();
        std::string watermarkText = "录制时间: " + std::to_string(elapsed) + " 秒";

        // 设置窗口透明度
        ImGui::SetNextWindowBgAlpha(0.4f); // 完全透明

        // 设置窗口位置
        ImGui::SetNextWindowPos(ImVec2(10 + 1050, 10));

        // 创建窗口
        ImGui::Begin("RecordingTimeWindow", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_AlwaysAutoResize);
        ImGui::Text("%s", watermarkText.c_str());
        ImGui::End();
    }
	ImGui::SameLine();
          break;
        }
        case 2:
        {
       if (ImGui::SliderFloat("骨骼距离", &绘制.骨骼距离限制, 0.0f, 300.0f, "%.0f", 1))
            绘制.保存配置();
          if (ImGui::SliderFloat("雷达左右", &绘制.按钮.雷达X, 0.0f, 2400.0f, "%.1f", 1))
            绘制.保存配置();
          if (ImGui::SliderFloat("雷达上下", &绘制.按钮.雷达Y, 0.0f, 1080.0f, "%.1f", 2))
            绘制.保存配置();
          break;
        }
        case 4:
        {
        if (ImGui::Combo("手持绘图", &绘制.按钮.手持绘图, "手持武器文字\0手持武器图片\0"))
        绘制.保存配置();
      //  if (ImGui::Combo("血条样式", &绘制.按钮.血条绘图, "默认样式\0圆圈血条\0简约血条\0"))
        绘制.保存配置();
        }
        }
        ImGui::EndChild();
        break;
      }

      case 1:
      {
        ImGui::BeginChild("##自瞄", ImVec2(-1, -1), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        static int AimMenu = 0;

        ImGui::BeginChild("##自瞄菜单", ImVec2(-1, 75), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        if (ImGui::Button("触摸自瞄"))
        {
          AimMenu = 0;
        }
        ImGui::SameLine();
        if (ImGui::Button("调节"))
        {
          AimMenu = 2;
        }
        ImGui::SameLine();
        if (ImGui::Button("打击部位"))
        {
          AimMenu = 1;
        }

        ImGui::EndChild();
        switch (AimMenu)
        {
        case 0:
        {
          if (ImGui::Checkbox("打开自瞄       ", &绘制.自瞄.初始化))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("自瞄开关       ", &绘制.自瞄.自瞄控件))
            绘制.保存配置();
          ImGui::SameLine();          
          if (ImGui::Checkbox("触摸位置       ", &绘制.自瞄.触摸位置))
            绘制.保存配置();
          
          if (ImGui::Checkbox("动态自瞄       ", &绘制.自瞄.动态自瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("准星射线       ", &绘制.自瞄.准星射线))
            绘制.保存配置();
         ImGui::SameLine();
          if (ImGui::Checkbox("倒地不瞄       ", &绘制.自瞄.倒地不瞄))
            绘制.保存配置();
          
          if (ImGui::Checkbox("人机不瞄       ", &绘制.自瞄.人机不瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("隐藏自瞄圈     ", &绘制.自瞄.隐藏自瞄圈))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("掉血自瞄       ", &绘制.自瞄.掉血自瞄))
            绘制.保存配置();
          
          if (ImGui::Checkbox("随机触摸(必点) ", &绘制.自瞄.随机触摸点))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("喷子自瞄       ", &绘制.自瞄.喷子自瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("开镜狙击自瞄   ", &绘制.自瞄.狙击自瞄))
            绘制.保存配置();

          if (ImGui::Checkbox("框内自瞄       ", &绘制.自瞄.框内自瞄))
            绘制.保存配置();
          if (ImGui::Checkbox("强锁自瞄       ", &绘制.自瞄.强锁自瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("软锁自瞄       ", &绘制.自瞄.软锁自瞄))
          {
            // float 自瞄速度 = 0.f; //&绘制.自瞄.自瞄速度
            // float 压枪力度 = 0.f;//&绘制.自瞄.压枪力度
            // float mk20压枪 = 0.f;//&绘制.mk20
       /*    if (绘制.自瞄.强锁自瞄)
            {
       

              绘制.自瞄.压枪力度 = 1.90f;
              绘制.自瞄.自瞄速度 = 1.20f;
              绘制.mk20 = 0.25f;
            }

            }
          }*/
            
            if (绘制.自瞄.软锁自瞄)
            {
              绘制.备份.压枪力度 = 绘制.自瞄.压枪力度;
              绘制.备份.自瞄速度 = 绘制.自瞄.自瞄速度;
              绘制.备份.mk20压枪 = 绘制.mk20;

              绘制.自瞄.压枪力度 = 3.5f;
              绘制.自瞄.自瞄速度 = 25.f;
              绘制.mk20 = 0.25f;
            }

            if (!绘制.自瞄.软锁自瞄)
            {
              绘制.自瞄.压枪力度 = 绘制.备份.压枪力度;
              绘制.自瞄.自瞄速度 = 绘制.备份.自瞄速度;
              绘制.mk20 = 绘制.备份.mk20压枪;
            }
          }
          ImGui::SameLine();
          ImGui::Text("");
          //if (ImGui::SliderFloat("触摸大小", &绘制.自瞄.触摸范围, 5, 600, "%.0f"))
            //绘制.保存配置();
            if (ImGui::SliderFloat("压枪力度", &绘制.自瞄.压枪力度, 0.1, 5, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("平滑速度", &绘制.自瞄.自瞄速度, 0.f, 80.f, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("触摸大小", &绘制.自瞄.触摸范围, 5, 600, "%.0f"))
            绘制.保存配置();
          break;
        }
        case 1:
        {

          if (ImGui::Combo("自瞄条件", &绘制.自瞄.自瞄条件, "开火\0开镜\0开火||开镜\0"))
            绘制.保存配置();
          if (ImGui::Combo("喷子自瞄触发条件", &绘制.自瞄.喷子自瞄条件, "持续锁定\0开镜锁定\0"))
            绘制.保存配置();
          if (ImGui::Combo("充电口方向", &绘制.自瞄.充电口方向, "右边\0左边\0"))
            绘制.保存配置();
          if (ImGui::Combo("自瞄优先", &绘制.自瞄.瞄准优先, "准星\0距离\0"))
            绘制.保存配置();
          if (ImGui::Combo("瞄准部位", &绘制.自瞄.瞄准部位, "头部\0胸部\0臀部\0"))
            绘制.保存配置();
          break;
        }
        case 2:
        {
          ImGui::Text("自瞄调节");
          ImGui::SameLine();
          ImGui::TextDisabled("(?)");
          if (ImGui::BeginItemTooltip())
          {
            ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
            ImGui::TextUnformatted(
                "参数调节详细\n"
                "1.自瞄速度 增则慢 减则快\n"
                "2.压枪力度 增则下抬 减则上抬\n");
            ImGui::PopTextWrapPos();
            ImGui::EndTooltip();
          }
          if (ImGui::SliderFloat("自瞄范围", &绘制.自瞄.自瞄范围, 10, 300, "%.0f"))
            绘制.保存配置();

     
          if (ImGui::SliderFloat("掉血自瞄概率", &绘制.自瞄.掉血自瞄数率, 5, 25, "%.0f"))
            绘制.保存配置();
            // if (ImGui::SliderFloat("三倍压枪", &绘制.自瞄.三倍压枪, 0.1, 5, "%.2f"))
            //  绘制.保存配置();

          if (ImGui::SliderFloat("预判速度", &绘制.自瞄.预判力度, 0.0, 2.0, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("扫车预判", &绘制.预判度.扫车, 0.0, 4.f, "%.2f"))
            绘制.保存配置();
            // if (ImGui::SliderFloat("扫车预判", &绘制.自瞄.扫车预判, 0.0, 300.0, "%.2f"))
            //  绘制.保存配置();
          if (ImGui::SliderFloat("腰射自瞄距离", &绘制.自瞄.腰射距离限制, 0.0, 300, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("开镜自瞄距离", &绘制.自瞄.自瞄距离限制, 0.0, 300, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("喷子距离限制", &绘制.自瞄.喷子距离限制, 0, 50, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("趴下压枪力度(反)", &绘制.自瞄.趴下位置调节, 0, 2, "%.2f"))
            绘制.保存配置();

          // ImGui::SliderFloat("MK20压枪力度", &绘制.mk20, 0.0, 2.0, "%.2f");
          // ImGui::SliderFloat("M417压枪力度", &绘制.m417, 0.0, 2.0, "%.2f");
          // ImGui::SliderFloat("轻型握把压枪(反)", &绘制.轻型压枪力度, 0.0f, 2.0f, "%.2f");
          break;
        }
        }
        ImGui::EndChild();
        break;
      }
      case 2:
      {
        ImGui::BeginChild("##物资", ImVec2(-1, -1), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        static int GoodsMenu = 0;

        ImGui::BeginChild("##物资菜单", ImVec2(-1, 75), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        if (ImGui::Button("枪械"))
          GoodsMenu = 0;
        ImGui::SameLine();
        if (ImGui::Button("子弹"))
          GoodsMenu = 1;
        ImGui::SameLine();
        if (ImGui::Button("头甲包"))
          GoodsMenu = 2;
        ImGui::SameLine();
        if (ImGui::Button("配件"))
          GoodsMenu = 3;
        ImGui::SameLine();
        if (ImGui::Button("药品"))
          GoodsMenu = 4;
        ImGui::SameLine();
        if (ImGui::Button("其他"))
          GoodsMenu = 5;
        ImGui::SameLine();
        if (ImGui::Button("地铁专用"))
          GoodsMenu = 6;
        ImGui::EndChild();

        switch (GoodsMenu)
        {
        case 0:
        {
          // 步枪 冲锋枪 霰弹枪 狙击枪
          if (ImGui::Checkbox("显示步枪", &绘制.按钮.显示步枪))
            绘制.保存配置();
          if (ImGui::Checkbox("冲锋枪械", &绘制.按钮.冲锋枪械))
            绘制.保存配置();
          if (ImGui::Checkbox("狙击枪械", &绘制.按钮.狙击枪械))
            绘制.保存配置();
          if (ImGui::Checkbox("散弹枪械", &绘制.按钮.散弹枪械))
            绘制.保存配置();
          if (ImGui::Checkbox("爆炸猎弓", &绘制.按钮.爆炸猎弓))
            绘制.保存配置();
          break;
        }
        case 1:
        {
          // 556 762 9mm 4.5mm 霰弹 信号弹 箭矢
          if (ImGui::Checkbox("5.56", &绘制.按钮.显示556子弹))
            绘制.保存配置();
          if (ImGui::Checkbox("7.62", &绘制.按钮.显示762子弹))
            绘制.保存配置();
          if (ImGui::Checkbox("9毫米", &绘制.按钮.显示9mm子弹))
            绘制.保存配置();
          if (ImGui::Checkbox(".45口径", &绘制.按钮.显示45mm子弹))
            绘制.保存配置();
          if (ImGui::Checkbox("霰弹", &绘制.按钮.显示霰弹))
            绘制.保存配置();
          if (ImGui::Checkbox("信号弹", &绘制.按钮.显示信号弹))
            绘制.保存配置();
          if (ImGui::Checkbox("箭矢", &绘制.按钮.显示箭矢))
            绘制.保存配置();

          break;
        }
        case 2:
        {
          if (ImGui::Checkbox("三级头", &绘制.按钮.显示三级头))
            绘制.保存配置();
          if (ImGui::Checkbox("三级甲", &绘制.按钮.显示三级甲))
            绘制.保存配置();
          if (ImGui::Checkbox("三级包", &绘制.按钮.显示三级包))
            绘制.保存配置();
          break;
        }
        case 3:
        {
          if (ImGui::Checkbox("子弹袋", &绘制.按钮.显示子弹袋))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("箭袋", &绘制.按钮.显示箭袋))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("激光瞄准器", &绘制.按钮.显示激光瞄准器))
            绘制.保存配置();

          if (ImGui::Checkbox("轻型握把", &绘制.按钮.显示轻型握把))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("半截握把", &绘制.按钮.显示半截握把))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("UZI枪托", &绘制.按钮.显示UZI枪托))
            绘制.保存配置();

          if (ImGui::Checkbox("狙击枪托", &绘制.按钮.显示狙击枪托))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("步枪枪托", &绘制.按钮.显示步枪枪托))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("狙击枪补偿器", &绘制.按钮.显示狙击枪补偿器))
            绘制.保存配置();

          if (ImGui::Checkbox("狙击枪消焰器", &绘制.按钮.显示狙击枪消焰器))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("狙击枪消音器", &绘制.按钮.显示狙击枪消音器))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("步枪消音器", &绘制.按钮.显示步枪消音器))
            绘制.保存配置();

          if (ImGui::Checkbox("步枪补偿器", &绘制.按钮.显示步枪补偿器))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("步枪消焰器", &绘制.按钮.显示步枪消焰器))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("冲锋枪消音器", &绘制.按钮.显示冲锋枪消音器))
            绘制.保存配置();

          if (ImGui::Checkbox("冲锋枪消焰器", &绘制.按钮.显示冲锋枪消焰器))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("拇指握把", &绘制.按钮.显示拇指握把))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("垂直握把", &绘制.按钮.显示垂直握把))
            绘制.保存配置();

          if (ImGui::Checkbox("直角握把", &绘制.按钮.显示直角握把))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("撞火枪托", &绘制.按钮.显示撞火枪托))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("霰弹快速", &绘制.按钮.显示霰弹快速))
            绘制.保存配置();

          if (ImGui::Checkbox("鸭嘴枪口", &绘制.按钮.显示鸭嘴枪口))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("霰弹收束", &绘制.按钮.显示霰弹收束))
            绘制.保存配置();
          break;
        }
        case 4:
        {
          if (ImGui::Checkbox("显示医疗箱", &绘制.按钮.显示医疗箱))
            绘制.保存配置();
          if (ImGui::Checkbox("显示急救包", &绘制.按钮.显示急救包))
            绘制.保存配置();
          if (ImGui::Checkbox("显示绷带", &绘制.按钮.显示绷带))
            绘制.保存配置();
          if (ImGui::Checkbox("显示可乐", &绘制.按钮.显示可乐))
            绘制.保存配置();
          if (ImGui::Checkbox("显示肾上腺素", &绘制.按钮.显示肾上腺素))
            绘制.保存配置();
          if (ImGui::Checkbox("显示止痛药", &绘制.按钮.显示止痛药))
            绘制.保存配置();
          break;
        }
        case 5:
        {
          if (ImGui::Checkbox("金插", &绘制.按钮.绘制金插))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("召回信号枪", &绘制.按钮.绘制信号枪))
            绘制.保存配置();
          break;
        }
        case 6:
        {
          if (ImGui::Checkbox("宝箱", &绘制.按钮.绘制宝箱))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("武器箱", &绘制.按钮.绘制武器箱))
            绘制.保存配置();
          if (ImGui::Checkbox("超级物资箱", &绘制.按钮.超级物资箱))
            绘制.保存配置();
          if (ImGui::Checkbox("隐藏已开启宝箱、药箱、武器箱", &绘制.按钮.隐藏已开启))
            绘制.保存配置();

          break;
        }
        }

        ImGui::EndChild();
        break;
      }
        case 4:
        {
          static int ColorSettings = 1;
          ImGui::Text("颜色配置");
          ImGui::Combo("当前配置", &ColorSettings, "人机\0真人\0");

          if (ColorSettings == 1)
          {
            if (ImGui::ColorEdit4("方框颜色", 绘制.Colorset[0].方框颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("射线颜色", 绘制.Colorset[0].射线颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("骨骼颜色", 绘制.Colorset[0].骨骼颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();

            if (ImGui::ColorEdit4("血量颜色", 绘制.Colorset[0].血量颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              // 绘制.保存配置();
            // if (ImGui::ColorEdit4("阵营颜色", 绘制.Colorset[0].阵营颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("距离颜色", 绘制.Colorset[0].距离颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("名称颜色", 绘制.Colorset[0].名称颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
          }
          else
          {
            if (ImGui::ColorEdit4("方框颜色", 绘制.Colorset[1].方框颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("射线颜色", 绘制.Colorset[1].射线颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("骨骼颜色", 绘制.Colorset[1].骨骼颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("血量颜色", 绘制.Colorset[1].血量颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            // if (ImGui::ColorEdit4("阵营颜色", 绘制.Colorset[1].阵营颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              // 绘制.保存配置();
            if (ImGui::ColorEdit4("距离颜色", 绘制.Colorset[1].距离颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
            if (ImGui::ColorEdit4("名称颜色", 绘制.Colorset[1].名称颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
              绘制.保存配置();
          }

          if (ImGui::ColorEdit4("物资颜色", 绘制.物资颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();

          break;
        }

        case 5:
        {
        ImGui::BeginChild("##粗细", ImVec2(-1, -1), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);
          ImGui::Text("绘图粗细配置");
          ImGui::SliderFloat("方框粗细", &绘制.按钮.方框粗细, 0.1, 5, "%.1f");
          ImGui::SliderFloat("射线粗细", &绘制.按钮.射线粗细, 0.1, 5, "%.1f");
          ImGui::SliderFloat("骨骼粗细", &绘制.按钮.骨骼粗细, 0.1, 5, "%.1f");
          ImGui::EndChild();
          break;
        }
        case 7:
        {
          ImGui::BeginChild("##内存功能", ImVec2(-1, -1), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);
                  // ImGui::Text( "人物加速(推荐1.15)");
                  ImGui::Checkbox("开启加速", &绘制.按钮.人物加速);
                 if (绘制.按钮.人物加速) {
                   绘制.读写.WriteFloat(绘制.地址.自身地址+0x3ebc,绘制.按钮.速度值);
                  }
                  ImGui::SliderFloat( "速度值", &绘制.按钮.速度值, 0, 1.09, "%.2f" );
                      //  ImGui::SliderFloat( "第一人称", &绘制.按钮.第一人称, 80, 200, "%.2f" );
          ImGui::Text( "超广角设置");
          ImGui::Checkbox("广角开启", &广角设置);
          ImGui::SliderFloat( "第三人称", &绘制.按钮.第三人称, 80, 300, "%.2f" );
        //  ImGui::SliderFloat( "第一人称", &绘制.按钮.第一人称, 80, 200, "%.2f" );
          ImGui::SliderInt("帧数调节", &绘制.按钮.帧率选项, 60, 144);
          ImGui::SliderFloat("触摸跟手率", &绘制.自瞄.触摸采样率, 200, 1200, "%.0f");
          ImGui::EndChild();
          break;
        }        
      }
      if (!悬浮窗)
      {
        悬浮球 = true;
        悬浮窗 = false;
        窗口状态 = true;
        IsBall = false;
        ImGui::SetWindowPos("悬浮图片", 绘制.Pos, ImGuiCond_Always);
      }
      绘制.winWidth = ImGui::GetWindowWidth();
      绘制.winHeith = ImGui::GetWindowHeight();
      g_window = ImGui::GetCurrentWindow();
      ImGui::End();
    } 
  }
      
  

    drawEnd();
}

void 布局::开启悬浮窗() {
    timer WindowDrawing;
    WindowDrawing.SetFps(120);
    WindowDrawing.AotuFPS_init();
    WindowDrawing.setAffinity();
    while (true) {
        // if (!绘制.Validate)
        // {
        //   exit(0);
        // }
        绘制悬浮窗();
        WindowDrawing.SetFps(绘制.按钮.当前帧率);
        WindowDrawing.AotuFPS();
        std::this_thread::sleep_for(1ms);
    }
}
