#ifndef MY_T3_VERIFY_S_H
#define MY_T3_VERIFY_S_H


#include <stdio.h>
#include <stdlib.h>

struct all_T3Check {
    char km_imei_[128] = {0};
    char km_key_[128] = {0};
    char 单码[32] = {0};
    char 解绑[32] = {0};
    char 公告[32] = {0};
    char 版本[32] = {0};  
    char app_key[64] = {0};
    char program_key[64] = {0};
    char base64_key[128] = {0};
};


//获取登陆结构
extern all_T3Check* get_all_T3CheckData();
//可执行文件快捷_登陆
void T3_Auto(int *yes, char *str_err);
//登陆
extern int T3_Login(all_T3Check *all_check, int *ret_yes, char *ret_err);
//解绑卡密
extern int T3_unbind_km(all_T3Check *all_check, char *ret_str);
//获取公告
extern void T3_get_announcement(all_T3Check *all_check, char *ret_str);
//获取最新版本
extern void T3_get_latestVersion(all_T3Check *all_check, char *ret_str);
//心跳线程
//extern void *T3_Circular_authentication(void *arg); 




extern void My_itoa(int num, char *str, int radix);
extern void My_获取时间戳( char sj[13]);
extern char* 加base64(const char* data, char BQAQSQEQ[]);
extern char *解base64(const char *data, char BQAQSQEQ[]);
extern char* getHEX(const char *string);


extern bool loadConfig_ver(char *cfgPath, void *data, size_t size);
extern bool saveConfig_ver(char *cfgPath, void *data, size_t size);
//get_
extern char *getAndroid_boot_serialno();
//get_
extern char* getMacAddresses(const char *str_name);

#endif //MY_T3_VERIFY_S_H








