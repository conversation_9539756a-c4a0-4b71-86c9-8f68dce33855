

#include <Draw.h>
#include <limits.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cstdio>
#include <fstream>
#include <iostream>
#include <random>
#include <set>
#include "辅助类.h"
#include <iostream>
#include <cstdlib>
#include <unistd.h>
#include <dirent.h>
#include <dlfcn.h>
#include <fcntl.h>
#include <limits.h>
#include <malloc.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/inotify.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/ptrace.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <time.h>
#include <unistd.h>
#include <csignal>
#include <cstdlib>
#include <ctime>
#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "图片调用.h"

#include <curl/curl.h>
#include <my_str.h>
#include "wcnm.h"
#include "My_T3verify.h"
#include "drivers/driver.h"

// 添加在include部分之后
extern int 音量();  // 声明音量函数

using namespace std;

int abs_ScreenX, abs_ScreenY;

int 无后台, 自瞄选项, 漏打;

布局 布局;
绘制 绘制;



int main(int argc, char* argv[]) {
all_T3Check* local_CheckData = get_all_T3CheckData();
        int yes = -1;
        char ErrStr[256] = {0};
        T3_Auto(&yes, (char*)&ErrStr);  // 里面有路径
        char announcement[256] = {0};   // 将announcement声明在if外部，使其能在后面访问
        if (yes == 86001769) {
            printf("到期时间:%s\n", ErrStr);

            // char announcement[256] = {0};
            T3_get_announcement(local_CheckData, (char*)&announcement);
     //       printf("\n公告 : %s\n", announcement);

            char version[32] = {0};
         
        } else {
           
        }
        printf("Hello, World!\n");

//int main() {
    printf("███    ███ ██    ██\n");
    printf("████  ████  ██  ██ \n");
    printf("██ ████ ██   ████  \n");
    printf("██  ██  ██    ██   \n");
    printf("██      ██    ██   \n");
    printf("██      ██    ██   \n");
printf("\033[1;36m等待清理封号残留，驱动残留\n");
 //   return 0;


printf("\033[34m");
printf("=====================================================\n");
printf("\033[33m");
printf("\033");
    	for (int i = 1; i < 21; i++)
        {
            usleep(100000);
            int v = 5* i;
            printf("\034  清理封号残留（包括驱动残留)中: %%%d\034", v);
            usleep(100);
        };
system("rm -rf /data/user/0/com.tencent.tmgp.pubgmhd/files/ano_tmp");
system("rm -rf /data/data/com.tencent.tmgp.pubgmhd/files/ano_tmp");
printf("\034\n");	//粉红色




        
     //   const static bool _ggSwitch = false;
        // 公告开关
printf("\033[36m");
printf("=====================================================\n");
        cout << "是否刷入驱动1刷入/2跳过" << endl;
        int select = 0;
        cin >> select;
        if (select == 1) {
            flushDriver();
            exit(0);
        }
        new std::thread(音量);
        绘制.防录屏 = 0;      // std::stoi(argv[1]);
        绘制.自瞄模式 = 0;    // std::stoi(argv[2]);
        绘制.无后台开关 = 0;  // std::stoi(argv[3]);
        绘制.漏打模式 = 0;    // std::stoi(argv[4]);

    //    printf("\033[37;1m");

      /*  if (绘制.无后台开关 == 0) {
        } else {
            pid_t pid = fork();
            if (pid > 0) {
                exit(0);
            }
        }*/

    printf("\033[1;31m内核禁开端口玩本 脚本搭配内防过检测和杀67摸块\n");
    printf("\033[1;31m特供内含服务器云控分析保护你的稳定性\n");
    printf("\033[1;31m------------------------------------------------\n");
    system("echo -e '\033[93m[+] 安卓版本:\033[0m' $(getprop ro.build.version.release)");
    system("echo -e '\033[93m[+] 设备机型:\033[0m' $(getprop ro.product.marketname)"); 
    std::cout <<"\033[1;34m[!] \n";
    std::cout <<"\033[1;36m[!] 温馨提示，低调演戏！\n";

  std::cout << std::endl << "1.KSU或AP选有后台\n2.无后台\n\n";
  std::cin >> 无后台;
  if (无后台 == 1) {
  }else {
    pid_t pids = fork();
    if (pids > 0) {
      exit(0);
    }
  }
  
        if (绘制.漏打模式 == 1) {
            绘制.漏打开关 = true;
        } else {
            绘制.漏打开关 = false;
        }

        布局.初始化程序();
        加载内存图片();
        /*
        std::cout << std::endl << "[-] 自瞄模式：\n[-] 1.触摸自瞄\n[-] 2.驱动自瞄\n\n";
        std::cin >> 自瞄选项;
        */
        if (绘制.自瞄模式 == 0 /*  and strstr(绘制.卡密,"ZM") != 0 */) {
            绘制.自瞄.预判力度 = 1.55f;
            绘制.自瞄主线程();
            // 绘制.掩体线程();
            绘制.GetTouch();
            绘制.按钮.自瞄选项 = true;
            // 绘制.是否开启自瞄页面 = true;
        }
        绘制.读取配置();

        // 绘制.自瞄主线程();
        // 绘制.GetTouch();

        布局.开启悬浮窗();
    
}

