#!/bin/bash

blue="\033[1;34m"
reset="\033[0m"

# 手动输入图片路径和输出文件路径
echo -e "${blue}请输入图片路径${reset}"
read input_image

echo -e "\033[1;32m请输入输出路径\033[0m"
read output_path

# 检查输入图片是否存在
if [ ! -f "$input_image" ]; then
    echo -e "\033[1;31m错误：图片 '$input_image' 不存在。\033[1;31m"
    exit 1
fi

# 从输入图片中读取二进制数据，并设置每行输出的字节数为 16
binary_data=$(xxd -i -c 16 "$input_image")

# 手动输入 image_data 变量的名称
echo -e "\033[1;33m请输入变量名称(例如:image_data):\033[0m"
read variable_name

# 提取输出文件名（不含路径）
output_file=$(basename "$input_image" .png)

# 判断输出文件名是否已包含 .h 扩展名，如果没有就添加
if [[ "$output_file" != *".h" ]]; then
    output_file="$output_file.h"
fi

# 拼接输出文件的完整路径
output_header="${output_path}"

# 编写C头文件内容
header_content="unsigned char $variable_name[] = {
$binary_data
};

"

# 将C头文件内容写入输出文件
echo "$header_content" > "$output_header"
echo -e "\033[1;36m处理完成\033[0m"