//微验网络验证//
//如果是AIDE编译jni，请将原main.cpp删除，将此注入好的文件改成main.cpp
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <dirent.h>
#include <pthread.h>
#include <fstream>
#include <string.h>
#include <time.h>
#include <malloc.h>
#include <iostream>
#include <fstream>
#include "res/weiyan.h"
#include "res/cJSON.h"
#include "res/cJSON.c"
#include "res/Encrypt.h"
#include<iostream>
#include<ctime>
using namespace std;
#include <Draw.h>
#include <limits.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cstdio>
#include <fstream>
#include <iostream>
#include <random>
#include <set>
#include "辅助类.h"
#include <iostream>
#include <cstdlib>
#include <unistd.h>
#include <dirent.h>
#include <dlfcn.h>
#include <fcntl.h>
#include <limits.h>
#include <malloc.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/inotify.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/ptrace.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <time.h>
#include <unistd.h>
#include <csignal>
#include <cstdlib>
#include <ctime>
#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>
#include "图片调用.h"
#include <curl/curl.h>
#include <my_str.h>
//#include "wcnm.h"
#include "drivers/driver.h"

// 添加在include部分之后
extern int 音量();  // 声明音量函数

using namespace std;

int abs_ScreenX, abs_ScreenY;

int 无后台, 自瞄选项, 漏打;

布局 布局;
绘制 绘制;
//云端更新
float versionone = 1;//修改这里
char *getSubstring(const char *const src, const char *const startTag, const char *const endTag)
{
const char *start = strstr(src, startTag);
    if (start == NULL)
    {
        return NULL;
    }
    start += strlen(startTag);

    const char *end = strstr(start, endTag);
    if (end == NULL)
    {
        return NULL;
    }

    size_t length = end - start;
    if (length == 0)
    {
        return NULL;
    }

    char *result = (char *)malloc(sizeof(char) * (length + 1));
    strncpy(result, start, length);
    result[length] = '\0';

    return result;
}

char *download()
{
	FILE *fp = popen("curl -s https://sharechain.qq.com/5fe09b183a7be3c77a432e8791890a1e", "r");//修改这里
	char *download;
	while (feof(fp) == 0)
	{
		char buf[512];
		fgets(buf, 512, fp);
		char bti[256];
		sscanf(buf, " <title>%[^<]</title>", bti);
		if ((download = strstr(buf, "[更新]")) != NULL)
		{
			pclose(fp);
			break;
		}
	}
	download = getSubstring(download, "[更新]", "[下载]");
	return download;
}


float version()
{
    FILE *fp = popen("curl -s https://sharechain.qq.com/5fe09b183a7be3c77a432e8791890a1e", "r");//修改这里
    char *version;
    while (feof(fp) == 0)
    {
        char buf[512];
        fgets(buf, 512, fp);
        char bti[256];
        sscanf(buf, " <title>%[^<]</title>", bti);
        if ((version = strstr(buf, "[云端]")) != NULL)
        {
            pclose(fp);
            break;
        }
    }
    version = getSubstring(version, "[云端]", "[版本]");
    float convertedVersion = atof(version);
    free(version);
    return convertedVersion;
    }



int main(int argc, char* argv[]) 
{
printf("\033[36m");
printf("=====================================================\n");
        cout << "是否刷入驱动1刷入/2跳过" << endl;
        int select = 0;
        cin >> select;
        if (select == 1) {
            flushDriver();
            exit(0);
        }
    const static char *_wyHost = "wy.llua.cn";
	const static char *_wyAppid = "21356";	
	const static char *_wyAppkey = "79Zw7zCIsZEZYf6";
	const static char *_wyRc4key = "2H5MRWggF0YYgb2";
	// 以上信息勿动
	
	const static char *_kmPath = "/sdcard/km";
	// 卡密路径

	const static char *_imeiPath = "/sdcard/imei";
	// 机器码路径
	
	const static bool _ggSwitch = false;
	// 公告开关

	printf("\033[35;1m");		// 粉红色
	printf("欢迎使用微验验证[%s]\n",_wyAppid);
	printf("\033[32;1m");		// 绿色
	printf("\n微验官网:http://llua.cn\n\n");
	printf("\033[33;1m");		// 黄色

	if (_ggSwitch){
	    char _ggUrl[1024];
	    sprintf(_ggUrl, "app=%s",_wyAppid);
    	char *_ggData = httppost(_wyHost,"api/?id=notice",_ggUrl);
    	char* _deggData=Decrypt(_ggData, _wyRc4key);
    	cJSON *_ggJson = cJSON_Parse(_deggData);
    	int _ggCode = cJSON_GetObjectItem(_ggJson, "code")->valueint;
    	if (_ggCode == 200){
    		cJSON *_ggMsg = cJSON_GetObjectItem(_ggJson, "msg");
            char *_appgg = cJSON_GetObjectItem(_ggMsg, "app_gg")->valuestring;
    	    printf("\n\n公告:%s\n\n",_appgg);
    	}
	}
	
	home_main:
	char _Kami[40];
	if (fopen(_kmPath, "r") == NULL)
	{
		printf("\033[31;1m");
		printf("请输入卡密:");
        char _inputKm[] = "";
	    scanf("%s",&_inputKm);
        FILE *fp = fopen(_kmPath, "w");
        if (fp != NULL) {
            fprintf(fp, "%s", _inputKm);
		    fclose(fp);
        }
        std::cout << "写入成功！正在重新验证卡密" << std::endl;
	}
	fscanf(fopen(_kmPath, "r"), "%s", &_Kami);
	char _Imei[40];
	if (fopen(_imeiPath, "r") == NULL)
	{
		printf("\033[31;1m");
		printf("设备码获取失败\n");
		srand(time(NULL));
        char* _Str = (char*)malloc((20 + 1) * sizeof(char));
        for (int i = 0; i < 20; i++) {
            int _randomNum = rand() % 26;
            _Str[i] = 'a' + _randomNum;
        }
        _Str[20] = '\0';
    
        FILE *fp = fopen(_imeiPath, "w");
        if (fp == NULL) {
            printf("文件创建失败");
            return 0;
        }
        fprintf(fp, "%s", _Str);
        fclose(fp);
        std::cout << "设备码已重新获取！正在重新验证卡密" << std::endl;
	}
	fscanf(fopen(_imeiPath, "r"), "%s", &_Imei);
	printf("卡密： %s\n设备码： %s\n\n", _Kami, _Imei);
	if (_Kami == "" or _Imei == "")
	{
		printf("\033[31;1m");
		printf("无设备码或者卡密");
		return 0;
	}
	time_t _Timet = time(NULL);
	int _Time = time(&_Timet);
    srand(time(NULL));
	char _Value[1024];
	char _Sign[1024];
	char _Data[1024];
	sprintf(_Value, "%d%d", _Time,rand());
	sprintf(_Sign, "kami=%s&markcode=%s&t=%d&%s", _Kami, _Imei, _Time, _wyAppkey);
	unsigned char *_SignData = (unsigned char *)_Sign;
	MD5_CTX md5c;
	MD5Init(&md5c);
	unsigned char _Decrypt[16];
	MD5Update(&md5c, _SignData, strlen((char *)_SignData));
	MD5Final(&md5c, _Decrypt);
	char _SignMd5[33] = { 0 };
	for (int i = 0; i < 16; i++)
	{
		sprintf(&_SignMd5[i * 2], "%02x", _Decrypt[i]);
	}
	sprintf(_Data, "kami=%s&markcode=%s&t=%d&sign=%s&value=%s", _Kami, _Imei, _Time, _SignMd5, _Value);
    char *_enData=Encrypt(_Data, _wyRc4key);
	char _deData[1024];
	sprintf(_deData, "&data=%s", _enData);
	char _deUrl[1024];
	sprintf(_deUrl, "api/?id=kmlogin&app=%s",_wyAppid);
	char *_loginData = httppost(_wyHost,_deUrl,_deData);
	char* _deloginData=Decrypt(_loginData, _wyRc4key);
	cJSON *_loginJson = cJSON_Parse(_deloginData);
	int _loginCode = cJSON_GetObjectItem(_loginJson, "tb0db9e64911b8da282f2ea7768bb5c63")->valueint;
	int _loginTime = cJSON_GetObjectItem(_loginJson, "vfd1b82368dc72885a47242bde756138e")->valueint;
	char *_loginMsg = cJSON_GetObjectItem(_loginJson, "fcf811eda92684c40750c0ed1df0307ec")->valuestring;
	char *_loginCheck = cJSON_GetObjectItem(_loginJson, "xe8fbda9a38abeec29a452c7a34190815")->valuestring;
	if (_loginCode == 146)
	{
		cJSON *_loginMsgs = cJSON_GetObjectItem(_loginJson, "fcf811eda92684c40750c0ed1df0307ec");
	    char *_checkCode = cJSON_GetObjectItem(_loginMsgs, "wbda8237a1eede29e5bde59b32f341cce")->valuestring;
		long _loginVip = cJSON_GetObjectItem(_loginMsgs, "ab4939ca57737cebc8ba08a232f812ba2")->valuedouble;
		long _loginId = cJSON_GetObjectItem(_loginMsgs, "l879ecb4c0b4ff1a5550873ee4c9edc51")->valuedouble;
		char _deCheck[1024];
		sprintf(_deCheck, "%d%s%s",_loginTime,_wyAppkey,_Value);
		unsigned char *_deCheckData = (unsigned char *)_deCheck;
		MD5_CTX md5c;
		MD5Init(&md5c);
		unsigned char _Decrypt[16];
		MD5Update(&md5c, _deCheckData, strlen((char *)_deCheckData));
		MD5Final(&md5c, _Decrypt);
		char _checkMd5[33] = { 0 };
		for (int i = 0; i < 16; i++)
		{
			sprintf(&_checkMd5[i * 2], "%02x", _Decrypt[i]);
		}
		if ((string)_checkCode != "8bb58ae0c049fbc8e9dbabda32ad04c1"){
		    return 0;
		}
		if ((string)_checkMd5 == _loginCheck)
		{
			printf("\033[32;1m");	// 绿色
			printf("登录成功\n");
			if (_loginVip)
			{
				char _vipTime[11];
				sprintf(_vipTime, "%ld", _loginVip);
				time_t _timeStamp = std::atoll(_vipTime);
				std::tm * _timeInfo = std::localtime(&_timeStamp);
				char _buffer[80];
				std::strftime(_buffer, sizeof(_buffer), "%Y-%m-%d %H:%M:%S", _timeInfo);
				std::cout << "到期时间：" << _buffer << std::endl;
				//到期自动退出
				signal(SIGALRM, _exit); 
                alarm(_loginVip-_Time); 
			}
		}
		else
		{
			printf("校验失败\n");
			remove(_kmPath);
		    goto home_main;
		    return 0;
		}
	}
	else
	{
		printf("\033[35;1m");	// 粉红色
		cout << _loginMsg << endl;
		remove(_kmPath);
		goto home_main;
		return 0;
	}
    printf("正在验证版本号...");
    float versionResult = version();
    printf("程序版本: %.2f\n", versionone);
    printf("云端版本: %.2f\n", versionResult);
    if (versionResult > versionone)
    {
        char *downloadLink = download();
        printf("下载链接: %s\n", downloadLink);
        system("rm -rf 耀沐寒多合一.sh");//修改这里文件名字
        char cmd[512];
        
        
        snprintf(cmd, sizeof(cmd), "curl -L -o 耀沐寒多合一.sh %s", downloadLink);//修改这里文件名字
        
        int ret = system(cmd);
        if (ret == 0)
        {	    
            printf("程序更新下载成功\n");
             
          
        }
        free(downloadLink);
        exit(0);
    } else {
        printf("无需更新\n");
    }





 printf("██╗  ██╗ █████╗ ██╗  ██╗ █████╗\n");
printf("██║ ██╔╝██╔══██╗██║ ██╔╝██╔══██╗\n");
printf("█████╔╝ ███████║█████╔╝ ███████║\n");
printf("██╔═██╗ ██╔══██║██╔═██╗ ██╔══██║\n");
printf("██║  ██╗██║  ██║██║  ██╗██║  ██║\n");
printf("╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝\n");

printf("\033[1;36m等待清理封号残留，驱动残留\n");
printf("\033[34m");
printf("=====================================================\n");
/*printf("\033[33m");
printf("\033");
    	for (int i = 1; i < 21; i++)
        {
            usleep(100000);
            int v = 5* i;
            printf("\034  清理封号残留（包括驱动残留)中: %%%d\034", v);
            usleep(100);
        };
system("rm -rf /data/user/0/com.tencent.tmgp.pubgmhd/files/ano_tmp");
system("rm -rf /data/data/com.tencent.tmgp.pubgmhd/files/ano_tmp");
printf("\034\n");	//粉红色*/printf("\033[33m"); // 设置文本颜色为黄色（进度条颜色）
    
    for (int i = 1; i <= 20; i++) {
        usleep(100000); // 100ms 延迟
        
        // 计算进度百分比（5% 步进）
        int progress = 5 * i;
        
        // 打印动态进度条
        printf("\033[2K\r"); // 清除当前行并回到行首
        printf("\033[33m[清理中]\033[0m "); // 黄色标签
        
        // 进度条样式
        printf("\033[32m["); // 绿色开始
        for (int j = 0; j < i; j++) printf("="); // 进度填充
        for (int j = i; j < 20; j++) printf(" "); // 剩余空白
        printf("]\033[0m "); // 绿色结束
        
        // 百分比显示
        printf("\033[36m%d%%\033[0m", progress); // 青色百分比
        
        fflush(stdout); // 强制刷新输出缓冲区
    }
    
    // 执行清理命令
    system("rm -rf /data/user/0/com.tencent.tmgp.pubgmhd/files/ano_tmp");
    system("rm -rf /data/data/com.tencent.tmgp.pubgmhd/files/ano_tmp");
    
    // 完成提示（粉色）
    printf("\n\033[35m✅ 清理完成！残留文件已删除。\033[0m\n");
    


    






        
     //   const static bool _ggSwitch = false;
  /*      // 公告开关
printf("\033[36m");
printf("=====================================================\n");
        cout << "是否刷入驱动1刷入/2跳过" << endl;
        int select = 0;
        cin >> select;
        if (select == 1) {
            flushDriver();
            exit(0);
        }*/
        new std::thread(音量);
        绘制.防录屏 = 0;      // std::stoi(argv[1]);
        绘制.自瞄模式 = 0;    // std::stoi(argv[2]);
        绘制.无后台开关 = 0;  // std::stoi(argv[3]);
        绘制.漏打模式 = 0;    // std::stoi(argv[4]);

    //    printf("\033[37;1m");

      /*  if (绘制.无后台开关 == 0) {
        } else {
            pid_t pid = fork();
            if (pid > 0) {
                exit(0);
            }
        }*/

    printf("\033[1;31m内核禁开端口玩本 脚本搭配内防过检测和杀67摸块\n");
    printf("\033[1;31m特供内含服务器云控分析保护你的稳定性\n");
    printf("\033[1;31m------------------------------------------------\n");
    system("echo -e '\033[93m[+] 安卓版本:\033[0m' $(getprop ro.build.version.release)");
    system("echo -e '\033[93m[+] 设备机型:\033[0m' $(getprop ro.product.marketname)"); 
    std::cout <<"\033[1;34m[!] \n";
    std::cout <<"\033[1;36m[!] 温馨提示，低调演戏！\n";

  std::cout << std::endl << "1.KSU或AP选有后台\n2.无后台\n\n";
  std::cin >> 无后台;
  if (无后台 == 1) {
  }else {
    pid_t pids = fork();
    if (pids > 0) {
      exit(0);
    }
  }
  
        if (绘制.漏打模式 == 1) {
            绘制.漏打开关 = true;
        } else {
            绘制.漏打开关 = false;
        }

        布局.初始化程序();
        加载内存图片();
        /*
        std::cout << std::endl << "[-] 自瞄模式：\n[-] 1.触摸自瞄\n[-] 2.驱动自瞄\n\n";
        std::cin >> 自瞄选项;
        */
        if (绘制.自瞄模式 == 0 /*  and strstr(绘制.卡密,"ZM") != 0 */) {
            绘制.自瞄.预判力度 = 1.55f;
            绘制.自瞄主线程();
            // 绘制.掩体线程();
            绘制.GetTouch();
            绘制.按钮.自瞄选项 = true;
            // 绘制.是否开启自瞄页面 = true;
        }
        绘制.读取配置();

        // 绘制.自瞄主线程();
        // 绘制.GetTouch();

        布局.开启悬浮窗();
    
}

